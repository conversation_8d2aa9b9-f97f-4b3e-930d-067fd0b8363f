@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 120 30% 96%;
    --foreground: 20 10% 10%;

    --card: 0 0% 100%;
    --card-foreground: 20 10% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 20 10% 10%;

    --primary: 122 45% 41%;
    --primary-foreground: 0 0% 100%;

    --secondary: 84 74% 42%;
    --secondary-foreground: 0 0% 100%;

    --muted: 120 10% 90%;
    --muted-foreground: 25 5% 45%;

    --accent: 84 74% 42%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 120 5% 88%;
    --input: 120 5% 88%;
    --ring: 122 45% 41%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 20 14% 4%;
    --foreground: 0 0% 98%;

    --card: 24 9% 10%;
    --card-foreground: 0 0% 98%;

    --popover: 24 9% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 122 45% 41%;
    --primary-foreground: 0 0% 100%;

    --secondary: 84 74% 42%;
    --secondary-foreground: 0 0% 98%;

    --muted: 12 6% 15%;
    --muted-foreground: 24 5% 65%;

    --accent: 12 6% 15%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 24 5% 18%;
    --input: 24 5% 18%;
    --ring: 122 45% 41%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .wallet-card {
    @apply relative overflow-hidden bg-gradient-to-br from-primary to-primary/80 text-white rounded-xl p-4 shadow-md;
  }

  .wallet-card::before {
    content: '';
    @apply absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-bl-full;
  }

  .nav-link {
    @apply flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors;
  }

  .nav-link.active {
    @apply bg-zb-green/10 text-zb-green font-medium;
  }

  .dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
  }

  .action-button {
    @apply flex flex-col items-center justify-center p-4 border rounded-xl hover:bg-muted/50 transition-colors;
  }
}
