"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Send, Receipt, Plus, Check, X, Wallet as WalletIcon } from "lucide-react";
import { NewRequestModal } from "@/components/NewRequestModal";
import { motion } from "framer-motion";

// Mock data for sent and received requests
const sentRequests = [
  {
    id: 1,
    recipient: "<PERSON> Doe",
    amount: 100,
    currency: "USD",
    status: "pending",
    date: "2024-03-20",
    description: "Lunch payment"
  },
  {
    id: 2,
    recipient: "<PERSON>",
    amount: 50,
    currency: "USD",
    status: "accepted",
    date: "2024-03-19",
    description: "Movie tickets"
  }
];

const receivedRequests = [
  {
    id: 1,
    sender: "<PERSON> Johnson",
    amount: 75,
    currency: "USD",
    status: "pending",
    date: "2024-03-20",
    description: "Dinner payment"
  },
  {
    id: 2,
    sender: "<PERSON>",
    amount: 30,
    currency: "USD",
    status: "accepted",
    date: "2024-03-19",
    description: "Coffee"
  }
];

export default function PaymentRequestsPage() {
  const [showNewRequest, setShowNewRequest] = useState(false);

  return (
    <div className="space-y-6">
      <NewRequestModal 
        open={showNewRequest} 
        onOpenChange={setShowNewRequest}
      />

      <div className="flex justify-end">
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            onClick={() => setShowNewRequest(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            New Request
          </Button>
        </motion.div>
      </div>

      <Tabs defaultValue="sent" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="sent">Sent Requests</TabsTrigger>
          <TabsTrigger value="received">Received Requests</TabsTrigger>
        </TabsList>

        <TabsContent value="sent" className="space-y-4">
          {sentRequests.map((request) => (
            <Card key={request.id}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>To: {request.recipient}</span>
                  <span className={`px-2 py-1 rounded-full text-sm ${
                    request.status === "pending" 
                      ? "bg-yellow-100 text-yellow-800" 
                      : "bg-green-100 text-green-800"
                  }`}>
                    {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                  </span>
                </CardTitle>
                <CardDescription>{request.date}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">{request.currency} {request.amount}</p>
                <p className="text-muted-foreground">{request.description}</p>
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                {request.status === "pending" && (
                  <>
                    <Button variant="outline" size="sm">
                      <X className="h-4 w-4 mr-1" /> Cancel
                    </Button>
                    <Button variant="outline" size="sm">
                      <Send className="h-4 w-4 mr-1" /> Remind
                    </Button>
                  </>
                )}
              </CardFooter>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="received" className="space-y-4">
          {receivedRequests.map((request) => (
            <Card key={request.id}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>From: {request.sender}</span>
                  <span className={`px-2 py-1 rounded-full text-sm ${
                    request.status === "pending" 
                      ? "bg-yellow-100 text-yellow-800" 
                      : "bg-green-100 text-green-800"
                  }`}>
                    {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                  </span>
                </CardTitle>
                <CardDescription>{request.date}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">{request.currency} {request.amount}</p>
                <p className="text-muted-foreground">{request.description}</p>
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                {request.status === "pending" && (
                  <>
                    <Button variant="outline" size="sm">
                      <X className="h-4 w-4 mr-1" /> Decline
                    </Button>
                    <Button variant="default" size="sm">
                      <Check className="h-4 w-4 mr-1" /> Pay Now
                    </Button>
                  </>
                )}
              </CardFooter>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
} 