"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, Sheet<PERSON>eader, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { Menu, Wallet, CreditCard, SendHorizontal, ReceiptIcon, History, Settings, QrCode, Building2, Send, Receipt, WalletIcon, ReceiptText, Bell, ChevronRight, ChevronLeft, X, Plus, Globe2 } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useWalletStore } from "@/lib/stores/wallet-store";
import { useUserStore } from "@/lib/stores/userStore";
import Image from "next/image";
import { PageTransition } from '@/components/animations/page-transition'
import { motion } from 'framer-motion'
import { MobileNav } from './mobile-nav'

// Personal User Navigation
const personalNavigation = [
  {
    name: "Dashboard",
    href: "/",
    icon: Wallet,
    description: "View account summary and recent activity"
  },
  {
    name: "Wallets",
    href: "/wallets",
    icon: WalletIcon,
    description: "Manage mobile money and bank accounts"
  },
  {
    name: "Send Money",
    href: "/send-money",
    icon: SendHorizontal,
    description: "Transfer funds to other accounts"
  },
  {
    name: "Pay Bills",
    href: "/pay-bills",
    icon: ReceiptText,
    description: "Pay utilities and subscriptions"
  },
  {
    name: "My Cards",
    href: "/cards",
    icon: CreditCard,
    description: "Virtual and physical cards"
  },
  {
    name: "International Remittances",
    href: "/international-remittances",
    icon: Globe2,
    description: "Send and receive international transfers"
  },
  {
    name: "Scan QR",
    href: "/scan-qr",
    icon: QrCode,
    description: "Make payments via QR code"
  },
  {
    name: "Payment Requests",
    href: "/payment-requests",
    icon: ReceiptIcon,
    description: "Create and track payment requests"
  },
  {
    name: "Transactions",
    href: "/transactions",
    icon: History,
    description: "View transaction history"
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    description: "Manage account settings"
  },
];

// Business User Navigation
const businessNavigation = [
  {
    name: "Business Dashboard",
    href: "/business",
    icon: Building2,
    description: "Business overview and metrics"
  },
  {
    name: "Accounts",
    href: "/business/accounts",
    icon: WalletIcon,
    description: "Manage business accounts"
  },
  {
    name: "Bulk Payments",
    href: "/business/bulk-payments",
    icon: SendHorizontal,
    description: "Process bulk payments and payroll"
  },
  {
    name: "Invoicing",
    href: "/business/invoices",
    icon: ReceiptText,
    description: "Create and manage invoices"
  },
  {
    name: "Team Management",
    href: "/business/team",
    icon: Building2,
    description: "Manage team members and permissions"
  },
  {
    name: "Reports & Analytics",
    href: "/business/reports",
    icon: History,
    description: "Business intelligence and reporting"
  },
  {
    name: "API Integration",
    href: "/business/api",
    icon: Settings,
    description: "API keys and integrations"
  },
  {
    name: "Business Settings",
    href: "/business/settings",
    icon: Settings,
    description: "Business account settings"
  },
];

// Agent User Navigation
const agentNavigation = [
  {
    name: "Agent Dashboard",
    href: "/agent",
    icon: Building2,
    description: "Agent overview and performance"
  },
  {
    name: "Cash Services",
    href: "/agent/cash-services",
    icon: WalletIcon,
    description: "Cash-in and cash-out services"
  },
  {
    name: "Customer Onboarding",
    href: "/agent/onboarding",
    icon: SendHorizontal,
    description: "Register new customers"
  },
  {
    name: "Float Management",
    href: "/agent/float",
    icon: ReceiptText,
    description: "Manage agent float balance"
  },
  {
    name: "Commission Tracking",
    href: "/agent/commissions",
    icon: History,
    description: "Track earnings and commissions"
  },
  {
    name: "Agent Settings",
    href: "/agent/settings",
    icon: Settings,
    description: "Agent account settings"
  },
];

interface DashboardShellProps {
  children: React.ReactNode
}

export function DashboardShell({ children }: DashboardShellProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user } = useUserStore();

  // Get navigation based on user type
  const getNavigation = () => {
    if (!user) return personalNavigation; // Default to personal if no user

    switch (user.userType) {
      case 'business':
        return businessNavigation;
      case 'agent':
        return agentNavigation;
      case 'admin':
        return businessNavigation; // Admin uses business navigation for now
      default:
        return personalNavigation;
    }
  };

  const navigation = getNavigation();

  const handleAddWallet = () => {
    router.push('/wallets/add')
  }

  return (
    <PageTransition>
      <div className="min-h-screen bg-background">
        {/* Mobile Header */}
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 md:hidden">
          <div className="container flex h-14 items-center px-4">
            <Button variant="ghost" size="icon" className="mr-2" onClick={() => setIsMobileMenuOpen(true)}>
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
            <div className="flex items-center space-x-2">
              <Image
                src="/logo.svg"
                alt="Logo"
                width={32}
                height={32}
                priority
                className="h-8 w-8"
              />
              <span className="font-semibold">ZBUniWallet</span>
            </div>
          </div>
        </header>

        {/* Desktop Sidebar */}
        <aside className="fixed inset-y-0 left-0 z-40 hidden w-64 border-r bg-card md:block">
          <div className="flex h-14 items-center border-b px-6">
            <div className="flex items-center space-x-2">
              <Image
                src="/logo.svg"
                alt="Logo"
                width={32}
                height={32}
                priority
                className="h-8 w-8"
              />
              <span className="font-semibold">ZBUniWallet</span>
            </div>
          </div>
          <nav className="space-y-1.5 p-1.5">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Button
                  key={item.href}
                  variant={pathname === item.href ? "secondary" : "ghost"}
                  className={`w-full justify-start px-2 py-1 h-auto whitespace-normal border border-border/40 ${
                    pathname === item.href
                      ? "bg-secondary text-secondary-foreground hover:bg-secondary/90 border-border/70"
                      : "hover:bg-accent hover:text-accent-foreground hover:border-border/70"
                  }`}
                  onClick={() => router.push(item.href)}
                >
                  <div className="flex flex-col items-start w-full space-y-0.5 overflow-hidden">
                    <div className="flex items-center gap-1.5 w-full">
                      <Icon className="h-3.5 w-3.5 flex-shrink-0" />
                      <span className="font-medium text-sm break-words">{item.name}</span>
                    </div>
                    <div className="w-full border-t border-border/50" />
                    <span className={`text-[10px] leading-none truncate w-full ${
                      pathname === item.href ? "text-secondary-foreground" : "text-muted-foreground"
                    }`}>{item.description}</span>
                  </div>
                </Button>
              );
            })}
          </nav>
        </aside>

        {/* Main Content */}
        <div className="flex flex-col md:pl-64">
          {/* Desktop Header */}
          <header className="sticky top-0 z-40 hidden h-14 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 md:block">
            <div className="container flex h-14 items-center justify-between px-4">
              <div className="flex items-center space-x-4">
                <h1 className="text-lg font-semibold">
                  {navigation.find((item) => item.href === pathname)?.name || "Dashboard"}
                </h1>
              </div>
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="icon" className="hover:bg-accent hover:text-accent-foreground">
                  <Bell className="h-5 w-5" />
                  <span className="sr-only">Notifications</span>
                </Button>
                <Button variant="ghost" size="icon" className="hover:bg-accent hover:text-accent-foreground">
                  <Settings className="h-5 w-5" />
                  <span className="sr-only">Settings</span>
                </Button>
              </div>
            </div>
          </header>

          {/* Page Content */}
          <main className="flex-1 pb-16 md:pb-0">
            <div className="container px-4 py-6 md:px-8 lg:px-12">
              {pathname === "/" ? (
                <div className="flex items-center justify-between space-y-2">
                  <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
                  <div className="flex items-center gap-4">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2"
                        onClick={handleAddWallet}
                      >
                        <Plus className="h-4 w-4" />
                        Add Wallet
                      </Button>
                    </motion.div>
                    <Button
                      variant="outline"
                      onClick={() => router.push('/wallets')}
                    >
                      View All
                    </Button>
                  </div>
                </div>
              ) : null}
            </div>
            <div className="container px-4 md:px-8 lg:px-12">
              {children}
            </div>
          </main>

          {/* Mobile Navigation */}
          <MobileNav />
        </div>

        {/* Mobile Menu */}
        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetContent side="left" className="w-64 p-0">
            <SheetHeader className="border-b p-4">
              <SheetTitle>Menu</SheetTitle>
            </SheetHeader>
            <nav className="space-y-1 p-4">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Button
                    key={item.href}
                    variant={pathname === item.href ? "secondary" : "ghost"}
                    className={`w-full justify-start gap-2 ${
                      pathname === item.href
                        ? "bg-secondary text-secondary-foreground hover:bg-secondary/90"
                        : "hover:bg-accent hover:text-accent-foreground"
                    }`}
                    onClick={() => {
                      router.push(item.href)
                      setIsMobileMenuOpen(false)
                    }}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </Button>
                );
              })}
            </nav>
          </SheetContent>
        </Sheet>
      </div>
    </PageTransition>
  );
}
