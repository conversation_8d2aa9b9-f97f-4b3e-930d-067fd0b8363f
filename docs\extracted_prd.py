import zipfile
import xml.etree.ElementTree as ET

def extract_text_from_docx(docx_path):
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            xml_content = zip_file.read('word/document.xml')
            root = ET.fromstring(xml_content)
            
            # Extract text from all text nodes
            text_content = []
            for elem in root.iter():
                if elem.text:
                    text_content.append(elem.text)
            
            return ' '.join(text_content)
    except Exception as e:
        return f'Error reading docx: {e}'

# Read the PRD document
content = extract_text_from_docx('docs/prd.docx')
print(content)
