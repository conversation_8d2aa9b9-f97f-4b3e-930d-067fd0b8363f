import { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useWalletStore } from '@/lib/stores/wallet-store'
import { Card } from '@/components/ui/card'
import Image from 'next/image'
import { cn } from '@/lib/utils'

interface TopUpModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  destinationWallet: {
    id: string;
    name: string;
    logo: string;
    type: string;
  };
}

export function TopUpModal({ open, onOpenChange, destinationWallet }: TopUpModalProps) {
  const { wallets } = useWalletStore()
  const [amount, setAmount] = useState('')
  const [sourceWalletId, setSourceWalletId] = useState('')

  const sourceWallets = wallets.filter(wallet => 
    wallet.id !== destinationWallet.id && 
    wallet.state === 'connected'
  )

  const handleTopUp = () => {
    // TODO: Implement top up logic
    console.log('Top up:', {
      amount,
      sourceWalletId,
      destinationWalletId: destinationWallet.id
    })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Top Up Wallet</DialogTitle>
          <DialogDescription>
            Add funds to your {destinationWallet.name} wallet
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Destination Wallet Card */}
          <Card className="p-4">
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 relative bg-white rounded-lg p-2 flex items-center justify-center">
                <Image
                  src={destinationWallet.logo}
                  alt={destinationWallet.name}
                  fill
                  className="object-contain"
                />
              </div>
              <div>
                <h3 className="font-medium">{destinationWallet.name}</h3>
                <p className="text-sm text-muted-foreground capitalize">{destinationWallet.type}</p>
              </div>
            </div>
          </Card>

          {/* Source Wallet Selection */}
          <div className="space-y-2">
            <Label htmlFor="source-wallet">Source Wallet</Label>
            <Select value={sourceWalletId} onValueChange={setSourceWalletId}>
              <SelectTrigger>
                <SelectValue placeholder="Select source wallet" />
              </SelectTrigger>
              <SelectContent>
                {sourceWallets.map((wallet) => (
                  <SelectItem key={wallet.id} value={wallet.id}>
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 relative">
                        <Image
                          src={wallet.logo}
                          alt={wallet.name}
                          fill
                          className="object-contain"
                        />
                      </div>
                      <span>{wallet.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Amount Input */}
          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount to top up"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleTopUp}
              disabled={!sourceWalletId || !amount}
            >
              Top Up
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  )
} 