'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { RefreshCw } from "lucide-react"
import { useState } from "react"
import { cn } from "@/lib/utils"
import { Wallet } from '@/lib/stores/wallet-store'
import { useWalletStore } from '@/lib/stores/wallet-store'

interface BalanceDisplayProps {
  wallet: Wallet
}

export function BalanceDisplay({ wallet }: BalanceDisplayProps) {
  const { selectedCurrency } = useWalletStore()
  const balance = wallet.balances[selectedCurrency]

  return (
    <div className="text-right">
      <div className="text-sm text-muted-foreground">
        {selectedCurrency} Balance
      </div>
      <div className="text-lg font-semibold">
        {balance.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}
      </div>
    </div>
  )
} 