"use client";

import { useState } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { wallets } from "@/lib/data";
import { getWalletBalance } from "@/lib/utils";
import Image from "next/image";
import { CheckCircleIcon, XCircleIcon, ChevronDownIcon, MoreHorizontalIcon, EyeIcon, ReceiptIcon, SendHorizontal, Check, X } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

// <PERSON><PERSON> received payment requests
const receivedRequests = [
  {
    id: "req-004",
    amount: 800.00,
    currency: "ZWL",
    description: "Monthly rent",
    requestor: "Samuel Landlord",
    requestorPhone: "0771234599",
    walletType: "EcoCash",
    status: "pending",
    date: "Mar 22, 2025",
    expiresOn: "Mar 29, 2025",
  },
  {
    id: "req-005",
    amount: 250.50,
    currency: "ZWL",
    description: "Group lunch payment",
    requestor: "Mary Colleague",
    requestorPhone: "0781234588",
    walletType: "OneMoney",
    status: "pending",
    date: "Mar 21, 2025",
    expiresOn: "Mar 28, 2025",
  },
  {
    id: "req-006",
    amount: 45.00,
    currency: "USD",
    description: "Subscription fee",
    requestor: "Netflix Zimbabwe",
    requestorPhone: "0731234522",
    walletType: "ZB UniWallet",
    status: "completed",
    date: "Mar 15, 2025",
    completedDate: "Mar 16, 2025",
  },
  {
    id: "req-007",
    amount: 35.00,
    currency: "ZWL",
    description: "Office supplies",
    requestor: "Office Manager",
    requestorPhone: "0771234555",
    walletType: "EcoCash",
    status: "declined",
    date: "Mar 10, 2025",
    declinedDate: "Mar 11, 2025",
  }
];

export default function ReceivedRequestsPage() {
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);
  const [walletOpen, setWalletOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("all");

  const selectedWalletData = selectedWallet
    ? wallets.find(w => w.id === selectedWallet)
    : null;

  // Filter requests based on status tab
  const filteredRequests = activeTab === "all"
    ? receivedRequests
    : receivedRequests.filter(request => request.status === activeTab);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">Pending</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">Paid</Badge>;
      case 'declined':
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">Declined</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <DashboardShell>
      <div className="mx-auto max-w-4xl space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Received Requests</h1>
            <p className="text-muted-foreground">
              Manage money requests you've received from others
            </p>
          </div>
        </div>

        <Card>
          <CardHeader className="px-6 pb-2">
            <Tabs defaultValue="all" onValueChange={setActiveTab} className="w-full">
              <TabsList>
                <TabsTrigger value="all">All Requests</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="completed">Paid</TabsTrigger>
                <TabsTrigger value="declined">Declined</TabsTrigger>
              </TabsList>
            </Tabs>
          </CardHeader>
          <CardContent className="px-6">
            {filteredRequests.length > 0 ? (
              <div className="space-y-4">
                {filteredRequests.map((request) => (
                  <div
                    key={request.id}
                    className={`flex items-center justify-between rounded-lg border p-4
                      ${request.status === 'completed' ? 'border-green-100 bg-green-50/40' :
                       request.status === 'declined' ? 'border-red-100 bg-red-50/40' :
                       'border-yellow-100 bg-yellow-50/40'}`}
                  >
                    <div className="flex items-start gap-4">
                      <div className="rounded-full bg-primary/10 p-2 h-10 w-10 flex items-center justify-center">
                        <Image
                          src={
                            request.walletType === "EcoCash"
                              ? "/images/wallets/ecocash.png"
                              : request.walletType === "OneMoney"
                                ? "/images/wallets/onemoney.png"
                                : "/images/zb-logo.png"
                          }
                          alt={request.walletType}
                          width={20}
                          height={20}
                          className="h-5 w-5 object-contain"
                        />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{request.description || "Payment Request"}</h3>
                          {getStatusBadge(request.status)}
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          From: {request.requestor} • {request.date}
                          {request.status === 'pending' && request.expiresOn && (
                            <span> • Expires: {request.expiresOn}</span>
                          )}
                        </div>
                        <div className="font-medium text-lg mt-1">
                          {request.currency} {request.amount.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {request.status === 'pending' && (
                        <>
                          <Button className="gap-2" size="sm">
                            <CheckCircleIcon className="h-4 w-4" />
                            <span>Pay Now</span>
                          </Button>
                          <Button variant="outline" size="sm" className="gap-2">
                            <XCircleIcon className="h-4 w-4" />
                            <span>Decline</span>
                          </Button>
                        </>
                      )}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="icon" className="h-8 w-8">
                            <MoreHorizontalIcon className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <EyeIcon className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          {request.status === 'pending' && (
                            <>
                              <DropdownMenuItem>
                                <CheckCircleIcon className="h-4 w-4 mr-2" />
                                Pay Now
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-destructive">
                                <XCircleIcon className="h-4 w-4 mr-2" />
                                Decline Request
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="rounded-full bg-muted p-6 mb-4">
                  <EyeIcon className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium">No requests found</h3>
                <p className="text-muted-foreground mt-1 max-w-md">
                  You don't have any payment requests from others yet, or no requests match your current filter.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>
              Choose which wallet to use for payments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <DropdownMenu open={walletOpen} onOpenChange={setWalletOpen}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className={`w-full justify-between ${!selectedWallet ? 'text-muted-foreground' : ''}`}
                    >
                      {selectedWalletData ? (
                        <div className="flex items-center gap-2">
                          <div className="relative h-5 w-5">
                            <Image
                              src={selectedWalletData.logo}
                              alt={selectedWalletData.name}
                              fill
                              className="object-contain"
                            />
                          </div>
                          <span>{selectedWalletData.name}</span>
                          <span className="ml-2 text-sm">
                            ({selectedWalletData.primaryCurrency} {getWalletBalance(selectedWalletData).toLocaleString()})
                          </span>
                        </div>
                      ) : (
                        <span>Select default payment wallet</span>
                      )}
                      <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-full min-w-[300px]" align="start">
                    {wallets.filter(wallet => wallet.isConnected).map((wallet) => (
                      <DropdownMenuItem
                        key={wallet.id}
                        className="flex cursor-pointer items-center justify-between p-3"
                        onClick={() => {
                          setSelectedWallet(wallet.id);
                          setWalletOpen(false);
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div className="relative h-6 w-6 overflow-hidden rounded-md">
                            <Image
                              src={wallet.logo}
                              alt={wallet.name}
                              fill
                              className="object-contain"
                            />
                          </div>
                          <div>
                            <div className="font-medium">{wallet.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {wallet.primaryCurrency} {getWalletBalance(wallet).toLocaleString(undefined, { minimumFractionDigits: 2 })}
                            </div>
                          </div>
                        </div>
                        {selectedWallet === wallet.id && (
                          <CheckCircleIcon className="h-4 w-4 text-primary" />
                        )}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <div className="rounded-lg border bg-muted/30 p-4">
                <div className="text-sm font-medium">Payment Preferences</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Set your default wallet for making payments and choose auto-payment options.
                </p>
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm" htmlFor="auto-pay">Auto-pay requests under ZWL 100</label>
                    <input type="checkbox" id="auto-pay" className="h-4 w-4" />
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm" htmlFor="notify">Notify me about new payment requests</label>
                    <input type="checkbox" id="notify" className="h-4 w-4" checked readOnly />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button>Save Preferences</Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardShell>
  );
}
