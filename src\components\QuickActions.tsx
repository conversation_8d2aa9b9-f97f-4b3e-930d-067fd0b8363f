"use client";

import Link from "next/link";
import {
  SendIcon,
  BanknoteIcon,
  DollarSignIcon,
  PlusCircleIcon,
  HistoryIcon,
  CreditCardIcon as CardIcon,
  ArrowUpRight,
  ArrowDownLeft,
  CreditCard,
  Receipt,
  Wallet,
  Globe2,
  QrCode,
  Clock
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"
import { AnimatedCard } from "@/components/animations/shared-animations"

const actions = [
  {
    icon: SendIcon,
    label: "Send Money",
    description: "Transfer money",
    color: "text-blue-500",
    bgColor: "bg-blue-100",
    href: "/send-money"
  },
  {
    icon: Receipt,
    label: "Pay Bills",
    description: "Pay your bills",
    color: "text-purple-500",
    bgColor: "bg-purple-100",
    href: "/pay-bills"
  },
  {
    icon: Globe2,
    label: "International Remittances",
    description: "Send money abroad",
    color: "text-green-500",
    bgColor: "bg-green-100",
    href: "/international-remittances"
  },
  {
    icon: BanknoteIcon,
    label: "Request Money",
    description: "Get paid instantly",
    color: "text-orange-500",
    bgColor: "bg-orange-100",
    href: "/request-money"
  },
  {
    icon: QrCode,
    label: "Scan QR",
    description: "Scan to pay",
    color: "text-purple-500",
    bgColor: "bg-purple-100",
    href: "/scan-qr"
  },
  {
    icon: Clock,
    label: "History",
    description: "View transactions",
    color: "text-gray-500",
    bgColor: "bg-gray-100",
    href: "/transactions"
  }
];

export function QuickActions() {
  return (
    <AnimatedCard>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5 text-primary" />
            Quick Actions
          </CardTitle>
          <CardDescription>Common transactions and services</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            {actions.map((action, index) => {
              const Icon = action.icon
              return (
                <motion.div
                  key={action.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Button
                    variant="outline"
                    className="w-full h-auto flex flex-col items-center gap-2 p-4 group transition-all duration-200 hover:border-primary"
                    asChild
                  >
                    <Link href={action.href}>
                      <div className={`rounded-full p-3 ${action.bgColor} ${action.color} transition-transform duration-200 group-hover:scale-110`}>
                        <Icon className="h-6 w-6" />
                      </div>
                      <div className="text-sm font-medium mt-2">{action.label}</div>
                      <div className="text-xs text-muted-foreground">{action.description}</div>
                    </Link>
                  </Button>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </AnimatedCard>
  );
}
