"use client";

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardShell } from '@/components/dashboard-shell'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { WalletCard } from '@/components/WalletCard'
import { useWalletStore } from '@/lib/stores/wallet-store'
import { 
  Plus, 
  Wallet as WalletIcon, 
  SendIcon, 
  Receipt, 
  CreditCardIcon, 
  QrCodeIcon, 
  HistoryIcon,
  ArrowDownIcon,
  ArrowUpIcon
} from 'lucide-react'
import { NewRequestModal } from '@/components/NewRequestModal'

export default function Home() {
  const router = useRouter()
  const { wallets, setSelectedWallet } = useWalletStore()
  const [showNewRequest, setShowNewRequest] = useState(false)

  const connectedWallets = wallets.filter(wallet => wallet.state === 'connected')

  const handleSendMoney = (walletId: string) => {
    setSelectedWallet(walletId)
    router.push('/send-money')
  }

  const handleReceiveMoney = () => {
    setShowNewRequest(true)
  }

  return (
    <DashboardShell>
      <div className="container px-4 sm:px-6 space-y-6 sm:space-y-8 pb-20 md:pb-8">
        <div className="flex flex-col items-center justify-center space-y-2 sm:space-y-4 text-center">
          <h1 className="text-2xl sm:text-3xl font-bold">Welcome Back, John!</h1>
          <p className="text-sm sm:text-base text-muted-foreground max-w-[600px]">
            Manage your digital wallet, send money, and track your transactions all in one place.
          </p>
        </div>

        <div>
          <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4">Quick Actions</h2>
          <div className="grid grid-cols-3 sm:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-4">
            {[
              { 
                icon: SendIcon, 
                label: "Send Money", 
                onClick: () => router.push('/send-money'), 
                bgColor: "bg-[#E8F5E9]", 
                iconColor: "text-[#4CAF50]" 
              },
              { 
                icon: Receipt, 
                label: "Pay Bills", 
                href: "/pay-bills", 
                bgColor: "bg-[#E3F2FD]", 
                iconColor: "text-[#2196F3]" 
              },
              { 
                icon: CreditCardIcon, 
                label: "Payment Request", 
                href: "/payment-requests", 
                bgColor: "bg-[#FFE0D6]", 
                iconColor: "text-[#FF5722]" 
              },
              { 
                icon: Plus, 
                label: "Top Up", 
                href: "/wallets", 
                bgColor: "bg-[#E8F5E9]", 
                iconColor: "text-[#4CAF50]" 
              },
              { 
                icon: QrCodeIcon, 
                label: "Scan QR", 
                href: "/scan-qr", 
                bgColor: "bg-[#F3E5F5]", 
                iconColor: "text-[#9C27B0]" 
              },
              { 
                icon: HistoryIcon, 
                label: "History", 
                href: "/transactions", 
                bgColor: "bg-[#F5F5F5]", 
                iconColor: "text-[#757575]" 
              },
            ].map((action, index) => (
              <Card 
                key={index} 
                className={`p-2 sm:p-4 flex flex-col items-center justify-center text-center cursor-pointer ${action.bgColor} hover:opacity-90 transition-opacity duration-200 border-none`}
                onClick={() => action.onClick ? action.onClick() : router.push(action.href)}
              >
                <action.icon className={`h-5 w-5 sm:h-6 sm:w-6 mb-1 sm:mb-2 ${action.iconColor}`} />
                <span className="text-xs sm:text-sm font-medium">{action.label}</span>
              </Card>
            ))}
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <h2 className="text-lg sm:text-xl font-semibold">My Wallets</h2>
            <Button variant="outline" size="sm" className="text-xs sm:text-sm" onClick={() => router.push('/wallets')}>View All</Button>
          </div>
          <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {connectedWallets.map((wallet) => (
              <WalletCard
                key={wallet.id}
                wallet={wallet}
                onSendMoney={handleSendMoney}
                onReceiveMoney={handleReceiveMoney}
              />
            ))}

            <Card className="relative overflow-hidden border-dashed hover:border-primary/50 transition-colors cursor-pointer">
              <Button
                variant="ghost"
                className="h-full w-full p-4 sm:p-6 flex flex-col items-center justify-center gap-2"
                onClick={() => router.push('/wallets/add')}
              >
                <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-muted-foreground" />
                <span className="text-xs sm:text-sm font-medium text-muted-foreground">Add Wallet</span>
              </Button>
            </Card>
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <h2 className="text-lg sm:text-xl font-semibold">Recent Transactions</h2>
            <Button variant="outline" size="sm" className="text-xs sm:text-sm" onClick={() => router.push('/transactions')}>View All</Button>
          </div>
          <div className="grid gap-3 sm:gap-4">
            {[
              {
                id: 1,
                type: "received",
                amount: 150.00,
                currency: "USD",
                description: "Payment from Jane Smith",
                date: "Today, 2:30 PM",
                status: "completed"
              },
              {
                id: 2,
                type: "sent",
                amount: 50.00,
                currency: "USD",
                description: "Coffee payment to Bob's Cafe",
                date: "Yesterday, 9:15 AM",
                status: "completed"
              },
              {
                id: 3,
                type: "received",
                amount: 75.00,
                currency: "USD",
                description: "Refund from Amazon",
                date: "Oct 15, 4:20 PM",
                status: "pending"
              }
            ].map((transaction) => (
              <Card key={transaction.id} className="p-3 sm:p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 sm:gap-4">
                    <div className={`p-1.5 sm:p-2 rounded-full ${
                      transaction.type === "received" 
                        ? "bg-green-100 text-green-600" 
                        : "bg-blue-100 text-blue-600"
                    }`}>
                      {transaction.type === "received" ? (
                        <ArrowDownIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                      ) : (
                        <ArrowUpIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                      )}
                    </div>
                    <div>
                      <p className="text-sm sm:text-base font-medium">{transaction.description}</p>
                      <p className="text-xs sm:text-sm text-muted-foreground">{transaction.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm sm:text-base font-medium ${
                      transaction.type === "received" 
                        ? "text-green-600" 
                        : "text-blue-600"
                    }`}>
                      {transaction.type === "received" ? "+" : "-"}
                      {transaction.currency} {transaction.amount.toFixed(2)}
                    </p>
                    <p className="text-xs sm:text-sm text-muted-foreground capitalize">{transaction.status}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {showNewRequest && (
          <NewRequestModal open={showNewRequest} onOpenChange={setShowNewRequest} />
        )}
      </div>
    </DashboardShell>
  )
}
