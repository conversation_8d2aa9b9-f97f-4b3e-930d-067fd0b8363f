"use client";

import { useState } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ArrowDownIcon, ArrowUpIcon, BoltIcon, WifiIcon, PhoneIcon, TvIcon, LucideIcon } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

type TransactionType = "send" | "receive" | "bill" | "airtime" | "data";
type TransactionStatus = "completed" | "pending" | "failed";
type Currency = "ZWL" | "USD";

interface Transaction {
  id: string;
  type: TransactionType;
  title: string;
  description: string;
  amount: number;
  currency: Currency;
  date: string;
  status: TransactionStatus;
  icon?: LucideIcon;
  logo?: string;
  category?: string;
}

// Demo transactions data
const transactions: Transaction[] = [
  {
    id: "1",
    type: "send",
    title: "Sent to <PERSON>",
    description: "Transfer to EcoCash",
    amount: -50,
    currency: "USD",
    date: "2024-03-15 14:30",
    status: "completed",
    logo: "/ecocash.png"
  },
  {
    id: "2",
    type: "receive",
    title: "Received from Jane Smith",
    description: "OneMoney Transfer",
    amount: 2500,
    currency: "ZWL",
    date: "2024-03-15 12:15",
    status: "completed",
    logo: "/onemoney.png"
  },
  {
    id: "3",
    type: "bill",
    title: "ZESA Electricity",
    description: "Meter: ********",
    amount: -3000,
    currency: "ZWL",
    date: "2024-03-14 16:45",
    status: "completed",
    icon: BoltIcon,
    category: "utilities"
  },
  {
    id: "4",
    type: "bill",
    title: "ZOL Fiber",
    description: "Account: ZF123456",
    amount: -75,
    currency: "USD",
    date: "2024-03-14 10:20",
    status: "completed",
    icon: WifiIcon,
    category: "internet"
  },
  {
    id: "5",
    type: "send",
    title: "Sent to Sarah Wilson",
    description: "Bank Transfer - CBZ",
    amount: -150,
    currency: "USD",
    date: "2024-03-13 09:15",
    status: "pending",
    logo: "/cbz.png"
  },
  {
    id: "6",
    type: "airtime",
    title: "Econet Airtime",
    description: "Top-up: +************",
    amount: -10,
    currency: "USD",
    date: "2024-03-13 08:30",
    status: "completed",
    icon: PhoneIcon,
    category: "telecom"
  },
  {
    id: "7",
    type: "bill",
    title: "DStv Premium",
    description: "Account: DS987654",
    amount: -45,
    currency: "USD",
    date: "2024-03-12 15:20",
    status: "failed",
    icon: TvIcon,
    category: "entertainment"
  },
  {
    id: "8",
    type: "receive",
    title: "Received from Bob Brown",
    description: "InnBucks Transfer",
    amount: 100,
    currency: "USD",
    date: "2024-03-12 11:45",
    status: "completed",
    logo: "/innbucks.png"
  }
];

const statusConfig = {
  completed: { className: "bg-green-500/10 text-green-500" },
  pending: { className: "bg-yellow-500/10 text-yellow-500" },
  failed: { className: "bg-red-500/10 text-red-500" }
} as const;

const getTransactionIcon = (transaction: Transaction): LucideIcon | null => {
  if (transaction.icon) return transaction.icon;
  
  switch (transaction.type) {
    case "send": return ArrowUpIcon;
    case "receive": return ArrowDownIcon;
    case "airtime": return PhoneIcon;
    case "data": return WifiIcon;
    default: return null;
  }
};

const getIconColor = (type: TransactionType): string => {
  switch (type) {
    case "send": return "text-red-500";
    case "receive": return "text-green-500";
    case "bill": return "text-blue-500";
    case "airtime": return "text-purple-500";
    case "data": return "text-blue-500";
    default: return "text-primary";
  }
};

export default function TransactionsPage() {
  const [selectedType, setSelectedType] = useState<"all" | TransactionType>("all");

  const filteredTransactions = transactions.filter(transaction => 
    selectedType === "all" || transaction.type === selectedType
  );

  return (
    <DashboardShell>
      <Card>
        <CardHeader>
          <CardTitle>Transactions</CardTitle>
          <CardDescription>View and manage your transaction history</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="w-full" onValueChange={(value) => setSelectedType(value as "all" | TransactionType)}>
            <TabsList className="mb-4">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="send">Sent</TabsTrigger>
              <TabsTrigger value="receive">Received</TabsTrigger>
              <TabsTrigger value="bill">Bills</TabsTrigger>
              <TabsTrigger value="airtime">Airtime</TabsTrigger>
            </TabsList>

            <TabsContent value={selectedType} className="mt-0">
              <div className="space-y-4">
                {filteredTransactions.map((transaction) => {
                  const Icon = getTransactionIcon(transaction);
                  return (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between rounded-lg border p-4 transition-all hover:bg-muted/50"
                    >
                      <div className="flex items-center gap-4">
                        {transaction.logo ? (
                          <div className="relative h-10 w-10 overflow-hidden rounded-full border bg-background p-2">
                            <Image
                              src={transaction.logo}
                              alt={transaction.title}
                              fill
                              className="object-contain"
                            />
                          </div>
                        ) : Icon ? (
                          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                            <Icon className={cn("h-5 w-5", getIconColor(transaction.type))} />
                          </div>
                        ) : null}
                        <div>
                          <div className="font-medium">{transaction.title}</div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>{transaction.description}</span>
                            {transaction.category && (
                              <>
                                <span>•</span>
                                <Badge variant="secondary" className="text-xs">
                                  {transaction.category}
                                </Badge>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className={cn(
                            "font-medium",
                            transaction.amount < 0 ? "text-red-500" : "text-green-500"
                          )}>
                            {transaction.amount > 0 ? "+" : ""}
                            {transaction.currency} {Math.abs(transaction.amount).toLocaleString()}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {new Date(transaction.date).toLocaleString()}
                          </div>
                        </div>
                        <Badge variant="secondary" className={cn(
                          "capitalize",
                          statusConfig[transaction.status].className
                        )}>
                          {transaction.status}
                        </Badge>
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </DashboardShell>
  );
} 