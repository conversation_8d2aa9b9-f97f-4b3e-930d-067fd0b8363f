"use client";

import { useState, useEffect } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { QrCode, Camera, AlertCircle } from "lucide-react";

export default function ScanQRPage() {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanResult, setScanResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Request camera permission
    navigator.mediaDevices.getUserMedia({ video: true })
      .then(() => {
        setHasPermission(true);
      })
      .catch((err) => {
        setHasPermission(false);
        setError("Camera access denied. Please enable camera access to scan QR codes.");
      });
  }, []);

  const handleScan = (result: string) => {
    setScanResult(result);
    // Here you would typically handle the scanned QR code data
    // For example, parsing the data and navigating to the appropriate page
    console.log("Scanned QR code:", result);
  };

  return (
    <DashboardShell>
      <Card>
        <CardHeader>
          <CardTitle>Scan QR Code</CardTitle>
          <CardDescription>Scan QR codes to make quick payments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Camera className="h-5 w-5" />
                    Scan QR Code
                  </CardTitle>
                  <CardDescription>
                    Position the QR code within the camera view
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {hasPermission === null ? (
                    <div className="flex items-center justify-center h-64 bg-muted rounded-lg">
                      <p>Initializing camera...</p>
                    </div>
                  ) : hasPermission ? (
                    <div className="relative aspect-square bg-muted rounded-lg overflow-hidden">
                      {/* Camera preview would go here */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <QrCode className="h-32 w-32 text-muted-foreground" />
                      </div>
                      <div className="absolute inset-0 border-2 border-primary/50 rounded-lg m-4" />
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-64 bg-muted rounded-lg gap-4">
                      <AlertCircle className="h-8 w-8 text-destructive" />
                      <p className="text-center text-destructive">{error}</p>
                      <Button onClick={() => window.location.reload()}>
                        Try Again
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <QrCode className="h-5 w-5" />
                    Scan History
                  </CardTitle>
                  <CardDescription>
                    Your recent QR code scans
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {scanResult ? (
                      <div className="p-4 bg-muted rounded-lg">
                        <p className="font-medium">Last scanned:</p>
                        <p className="text-sm text-muted-foreground break-all">
                          {scanResult}
                        </p>
                      </div>
                    ) : (
                      <p className="text-center text-muted-foreground">
                        No recent scans
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  );
} 