import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface Transaction {
  id: string
  type: 'send' | 'receive' | 'request' | 'bill'
  amount: number
  currency: string
  status: 'pending' | 'completed' | 'failed'
  timestamp: string
  description: string
  sender?: string
  recipient?: string
  billType?: string
  billReference?: string
}

interface TransactionState {
  transactions: Transaction[]
  addTransaction: (transaction: Transaction) => void
  updateTransaction: (id: string, updates: Partial<Transaction>) => void
}

export const useTransactionStore = create<TransactionState>()(
  persist(
    (set) => ({
      transactions: [],
      addTransaction: (transaction) =>
        set((state) => ({
          transactions: [...state.transactions, transaction],
        })),
      updateTransaction: (id, updates) =>
        set((state) => ({
          transactions: state.transactions.map((t) =>
            t.id === id ? { ...t, ...updates } : t
          ),
        })),
    }),
    {
      name: 'transaction-storage',
    }
  )
) 