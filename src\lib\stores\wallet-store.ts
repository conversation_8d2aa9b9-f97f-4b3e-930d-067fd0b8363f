import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type WalletType = 'mobile' | 'bank';
export type WalletConnectionState = 'connected' | 'pending' | 'disconnected' | 'suspended';
export type Currency = 'ZWL' | 'USD';

export interface Wallet {
  id: string;
  name: string;
  logo: string;
  logoAlt: string;
  type: WalletType;
  state: WalletConnectionState;
  balances: {
    ZWL: number;
    USD: number;
  };
  primaryCurrency: Currency;
  isConnected: boolean;
  accountNumber?: string;
  phoneNumber?: string;
  branch?: string;
}

interface WalletStore {
  wallets: Wallet[];
  selectedWallet: string | null;
  selectedCurrency: Currency;
  setSelectedWallet: (walletId: string | null) => void;
  setSelectedCurrency: (currency: Currency) => void;
  updateWalletBalance: (walletId: string, currency: Currency, amount: number) => void;
  getWalletBalance: (walletId: string, currency?: Currency) => number;
  getWalletById: (walletId: string) => Wallet | undefined;
  refreshWalletBalances: () => Promise<void>;
  addWallet: (wallet: Omit<Wallet, 'id' | 'isConnected'>) => void;
  updateWallet: (id: string, updates: Partial<Wallet>) => void;
  deleteWallet: (id: string) => void;
}

export const useWalletStore = create<WalletStore>()(
  persist(
    (set, get) => ({
      wallets: [
        {
          id: '1',
          name: 'EcoCash',
          type: 'mobile',
          balances: {
            ZWL: 5325.75,
            USD: 150.25
          },
          isConnected: true,
          state: 'connected',
          logo: '/images/wallets/ecocash.png',
          logoAlt: 'EcoCash Logo',
          primaryCurrency: 'USD'
        },
        {
          id: '2',
          name: 'OneMoney',
          type: 'mobile',
          balances: {
            ZWL: 2150.50,
            USD: 75.50
          },
          isConnected: true,
          state: 'connected',
          logo: '/images/wallets/onemoney.png',
          logoAlt: 'OneMoney Logo',
          primaryCurrency: 'USD'
        },
        {
          id: '3',
          name: 'InnBucks',
          type: 'mobile',
          balances: {
            ZWL: 1875.25,
            USD: 45.75
          },
          isConnected: false,
          state: 'pending',
          logo: '/images/wallets/innbucks.png',
          logoAlt: 'InnBucks Logo',
          primaryCurrency: 'USD'
        },
        {
          id: '4',
          name: "O'mari",
          type: 'mobile',
          balances: {
            ZWL: 950.00,
            USD: 25.00
          },
          isConnected: false,
          state: 'suspended',
          logo: '/images/wallets/omari-logo.png',
          logoAlt: "O'mari Logo",
          primaryCurrency: 'USD'
        },
        {
          id: '5',
          name: 'ZB Bank',
          type: 'bank',
          balances: {
            ZWL: 15250.00,
            USD: 500.00
          },
          isConnected: true,
          state: 'connected',
          logo: '/images/zb-logo.png',
          logoAlt: 'ZB Bank Logo',
          primaryCurrency: 'USD'
        }
      ],
      selectedWallet: null,
      selectedCurrency: 'USD',

      setSelectedWallet: (walletId) => {
        set({ selectedWallet: walletId });
      },

      setSelectedCurrency: (currency) => {
        set({ selectedCurrency: currency });
      },

      updateWalletBalance: (walletId, currency, amount) => {
        set((state) => ({
          wallets: state.wallets.map((wallet) =>
            wallet.id === walletId
              ? {
                  ...wallet,
                  balances: {
                    ...wallet.balances,
                    [currency]: amount,
                  },
                }
              : wallet
          ),
        }));
      },

      getWalletBalance: (walletId, currency) => {
        const wallet = get().wallets.find((w) => w.id === walletId);
        if (!wallet) return 0;

        if (currency) {
          return wallet.balances[currency];
        }
        return wallet.balances[wallet.primaryCurrency];
      },

      getWalletById: (walletId) => {
        return get().wallets.find((w) => w.id === walletId);
      },

      refreshWalletBalances: async () => {
        // Simulate API call to refresh balances
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real app, this would be an API call to get fresh balances
        // For now, we'll just simulate some random changes
        set((state) => ({
          wallets: state.wallets.map(wallet => ({
            ...wallet,
            balances: {
              ZWL: wallet.balances.ZWL + (Math.random() * 1000 - 500),
              USD: wallet.balances.USD + (Math.random() * 10 - 5)
            }
          }))
        }));
      },

      addWallet: (walletData) => {
        set((state) => ({
          wallets: [
            ...state.wallets,
            {
              ...walletData,
              id: crypto.randomUUID(),
              isConnected: true
            }
          ]
        }));
      },

      updateWallet: (id, updates) => {
        set((state) => ({
          wallets: state.wallets.map((wallet) =>
            wallet.id === id ? { ...wallet, ...updates } : wallet
          ),
        }));
      },

      deleteWallet: (id) => {
        set((state) => ({
          wallets: state.wallets.filter((wallet) => wallet.id !== id),
        }));
      }
    }),
    {
      name: 'wallet-storage'
    }
  )
); 