'use client'

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card } from "@/components/ui/card"
import { ChevronDown } from "lucide-react"
import Image from "next/image"
import { useWalletStore, type Wallet } from "@/lib/stores/wallet-store"
import { useState } from "react"

interface WalletSelectorProps {
  selectedWallet?: Wallet | null
  onWalletSelect: (wallet: Wallet) => void
  className?: string
}

export function WalletSelector({ selectedWallet, onWalletSelect, className }: WalletSelectorProps) {
  const { wallets } = useWalletStore()
  const [selectedCurrency, setSelectedCurrency] = useState<"ZWL" | "USD">("ZWL")

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Select Source Wallet</h2>
      <p className="text-sm text-muted-foreground">Choose the wallet you want to send money from</p>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-between bg-muted/30 hover:bg-muted/50 border-0 h-auto p-4"
          >
            {selectedWallet ? (
              <div className="flex items-center gap-3">
                <div className="h-8 w-8 relative">
                  <Image
                    src={selectedWallet.logo}
                    alt={selectedWallet.name}
                    fill
                    className="object-contain"
                  />
                </div>
                <span>{selectedWallet.name}</span>
              </div>
            ) : (
              <span>Select a wallet</span>
            )}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-full min-w-[300px]">
          {wallets
            .filter(wallet => wallet.state === 'connected')
            .map(wallet => (
              <DropdownMenuItem
                key={wallet.id}
                onClick={() => onWalletSelect(wallet)}
                className="p-3"
              >
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 relative">
                    <Image
                      src={wallet.logo}
                      alt={wallet.name}
                      fill
                      className="object-contain"
                    />
                  </div>
                  <span>{wallet.name}</span>
                </div>
              </DropdownMenuItem>
            ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {selectedWallet && (
        <Card className="p-4">
          <p className="text-sm text-muted-foreground mb-2">Available Balance</p>
          <div className="flex items-center gap-6">
            <div>
              <p className="text-xl font-semibold">
                ZWL {selectedWallet.balances.ZWL.toLocaleString()}
              </p>
            </div>
            <div className="h-8 w-px bg-border"></div>
            <div>
              <p className="text-xl font-semibold">
                USD {selectedWallet.balances.USD.toLocaleString()}
              </p>
            </div>
          </div>
        </Card>
      )}

      {selectedWallet && (
        <div className="space-y-2">
          <p className="text-sm font-medium">Select Currency to Send From</p>
          <div className="flex gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="currency"
                value="ZWL"
                checked={selectedCurrency === "ZWL"}
                onChange={(e) => setSelectedCurrency(e.target.value as "ZWL" | "USD")}
                className="h-4 w-4 border-primary text-primary"
              />
              <span>
                ZWL Balance ({selectedWallet.balances.ZWL.toLocaleString()})
              </span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="currency"
                value="USD"
                checked={selectedCurrency === "USD"}
                onChange={(e) => setSelectedCurrency(e.target.value as "ZWL" | "USD")}
                className="h-4 w-4 border-primary text-primary"
              />
              <span>
                USD Balance ({selectedWallet.balances.USD.toLocaleString()})
              </span>
            </label>
          </div>
        </div>
      )}
    </div>
  )
} 