"use client";

import { useState } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { wallets } from "@/lib/data";
import { getWalletBalance } from "@/lib/utils";
import Image from "next/image";
import { 
  QrCodeIcon, 
  ClipboardIcon, 
  ShareIcon, 
  ChevronDownIcon, 
  CheckIcon, 
  PlusIcon, 
  MoreHorizontalIcon, 
  RefreshCwIcon, 
  XIcon,
  CalendarIcon,
  BuildingIcon,
  UserIcon,
  FileTextIcon,
  SendIcon,
  SendHorizontal,
  ReceiptIcon,
  Bell
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/components/ui/use-toast";

interface PaymentRequest {
  id: string
  amount: number
  currency: string
  status: 'pending' | 'accepted' | 'rejected' | 'expired'
  createdAt: string
  expiresAt: string
  description: string
  recipient: {
    id: string
    name: string
    email: string
  }
  senderWallet: {
    id: string
    name: string
    logo: string
  }
}

// Mock sent payment requests
const sentRequests = [
  {
    id: "req-001",
    amount: 500.00,
    currency: "ZWL",
    description: "Dinner bill",
    recipient: "John Doe",
    recipientPhone: "0771234567",
    walletType: "EcoCash",
    status: "pending",
    date: "Mar 22, 2025",
    expiresAfter: "7d",
  },
  {
    id: "req-002",
    amount: 750.50,
    currency: "ZWL",
    description: "Utility bill",
    recipient: "Jane Smith",
    recipientPhone: "0781234567",
    walletType: "OneMoney",
    status: "completed",
    date: "Mar 21, 2025",
    expiresAfter: "never",
  },
  {
    id: "req-003",
    amount: 120.00,
    currency: "USD",
    description: "Internet bill",
    recipient: "Bob Johnson",
    recipientPhone: "0731234567",
    walletType: "ZB UniWallet",
    status: "expired",
    date: "Mar 15, 2025",
    expiresAfter: "24h",
  }
];

interface RequestFormData {
  type: "personal" | "business";
  recipientName: string;
  recipientEmail: string;
  recipientPhone: string;
  amount: string;
  currency: "ZWL" | "USD";
  dueDate: Date | undefined;
  description: string;
  // Business specific fields
  companyName?: string;
  vatNumber?: string;
  invoiceNumber?: string;
  itemizedBilling: boolean;
  items: {
    description: string;
    quantity: number;
    unitPrice: number;
  }[];
  // Personal specific fields
  relationship?: string;
  sendReminders: boolean;
  reminderFrequency?: "daily" | "weekly";
}

export default function SentRequestsPage() {
  const { toast } = useToast();
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);
  const [walletOpen, setWalletOpen] = useState(false);
  const [amount, setAmount] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [expiry, setExpiry] = useState<string>("never");
  const [activeTab, setActiveTab] = useState<string>("all");
  const [formData, setFormData] = useState<RequestFormData>({
    type: "personal",
    recipientName: "",
    recipientEmail: "",
    recipientPhone: "",
    amount: "",
    currency: "USD",
    dueDate: undefined,
    description: "",
    itemizedBilling: false,
    items: [],
    sendReminders: false
  });
  const [showNewRequest, setShowNewRequest] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<PaymentRequest | null>(null);

  const selectedWalletData = selectedWallet
    ? wallets.find(w => w.id === selectedWallet)
    : null;

  const isFormValid = selectedWallet && amount && Number(amount) > 0;

  // Filter requests based on status tab
  const filteredRequests = activeTab === "all"
    ? sentRequests
    : sentRequests.filter(request => request.status === activeTab);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">Pending</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">Completed</Badge>;
      case 'expired':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-300">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleInputChange = (
    field: keyof RequestFormData,
    value: RequestFormData[keyof RequestFormData]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addBillingItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { description: "", quantity: 1, unitPrice: 0 }]
    }));
  };

  const updateBillingItem = (
    index: number,
    field: keyof RequestFormData["items"][0],
    value: RequestFormData["items"][0][keyof RequestFormData["items"][0]]
  ) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const removeBillingItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const calculateTotal = () => {
    if (formData.itemizedBilling) {
      return formData.items.reduce((total, item) => 
        total + (item.quantity * item.unitPrice), 0
      );
    }
    return parseFloat(formData.amount) || 0;
  };

  const generateInvoiceNumber = () => {
    const prefix = "INV";
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, "0");
    return `${prefix}-${timestamp}-${random}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success!",
        description: `Successfully sent payment request to ${formData.recipientName}.`,
      });

      // Reset form
      setFormData({
        type: "personal",
        recipientName: "",
        recipientEmail: "",
        recipientPhone: "",
        amount: "",
        currency: "USD",
        dueDate: undefined,
        description: "",
        itemizedBilling: false,
        items: [],
        sendReminders: false
      });
      
      setShowNewRequest(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send payment request. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <DashboardShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Payment Requests</h1>
            <p className="text-muted-foreground">
              Request payments from individuals or businesses
            </p>
          </div>
          <Button onClick={() => setShowNewRequest(true)} className="gap-2">
            <PlusIcon className="h-4 w-4" />
            New Request
          </Button>
        </div>

        {showNewRequest && (
          <Card className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <Label>Request Type</Label>
                <RadioGroup
                  value={formData.type}
                  onValueChange={(value: "personal" | "business") =>
                    setFormData({ ...formData, type: value })
                  }
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="personal" id="personal" />
                    <Label htmlFor="personal">Personal Request</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="business" id="business" />
                    <Label htmlFor="business">Business Request</Label>
                  </div>
                </RadioGroup>
              </div>

              {formData.type === "business" && (
                <div className="space-y-4">
                  <div>
                    <Label>Business Name</Label>
                    <Input
                      value={formData.companyName}
                      onChange={(e) =>
                        setFormData({ ...formData, companyName: e.target.value })
                      }
                      placeholder="Enter business name"
                    />
                  </div>
                  <div>
                    <Label>Invoice Number</Label>
                    <Input
                      value={formData.invoiceNumber}
                      onChange={(e) =>
                        setFormData({ ...formData, invoiceNumber: e.target.value })
                      }
                      placeholder="Enter invoice number"
                    />
                  </div>
                </div>
              )}

              <div>
                <Label>Recipient</Label>
                <Input
                  value={formData.recipientName}
                  onChange={(e) =>
                    setFormData({ ...formData, recipientName: e.target.value })
                  }
                  placeholder={formData.type === "business" ? "Contact person name" : "Recipient name"}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Amount</Label>
                  <Input
                    type="number"
                    value={formData.amount}
                    onChange={(e) =>
                      setFormData({ ...formData, amount: e.target.value })
                    }
                    placeholder="Enter amount"
                  />
                </div>
                <div>
                  <Label>Currency</Label>
                  <Select
                    value={formData.currency}
                    onValueChange={(value: "ZWL" | "USD") =>
                      setFormData({ ...formData, currency: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ZWL">ZWL</SelectItem>
                      <SelectItem value="USD">USD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Due Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.dueDate ? (
                        format(formData.dueDate, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.dueDate}
                      onSelect={(date) =>
                        setFormData({ ...formData, dueDate: date })
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label>Description</Label>
                <Textarea
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Enter request description"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.sendReminders}
                  onCheckedChange={(checked) =>
                    setFormData({ ...formData, sendReminders: checked })
                  }
                />
                <Label>Send payment reminder</Label>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowNewRequest(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Send Request</Button>
              </div>
            </form>
          </Card>
        )}

        <Card>
          <CardHeader className="px-6 pb-2">
            <Tabs defaultValue="all" onValueChange={setActiveTab} className="w-full">
              <TabsList>
                <TabsTrigger value="all">All Requests</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
                <TabsTrigger value="expired">Expired</TabsTrigger>
              </TabsList>
            </Tabs>
          </CardHeader>
          <CardContent className="px-6">
            {filteredRequests.length > 0 ? (
              <div className="space-y-4">
                {filteredRequests.map((request) => (
                  <div
                    key={request.id}
                    className={`flex items-center justify-between rounded-lg border p-4
                      ${request.status === 'completed' ? 'border-green-100 bg-green-50/40' :
                       request.status === 'expired' ? 'border-gray-100 bg-gray-50/40' :
                       'border-yellow-100 bg-yellow-50/40'}`}
                  >
                    <div className="flex items-start gap-4">
                      <div className="rounded-full bg-primary/10 p-2 h-10 w-10 flex items-center justify-center">
                        <ShareIcon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{request.description || "Payment Request"}</h3>
                          {getStatusBadge(request.status)}
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          To: {request.recipient} ({request.walletType}) • {request.date}
                        </div>
                        <div className="font-medium text-lg mt-1">
                          {request.currency} {request.amount.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {request.status === 'pending' && (
                        <>
                          <Button variant="outline" size="icon" className="h-8 w-8" title="Resend Request">
                            <RefreshCwIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="icon" className="h-8 w-8" title="Cancel Request">
                            <XIcon className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="icon" className="h-8 w-8">
                            <MoreHorizontalIcon className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Copy Payment Link</DropdownMenuItem>
                          <DropdownMenuItem>Download QR Code</DropdownMenuItem>
                          {request.status === 'pending' && (
                            <DropdownMenuItem className="text-destructive">Cancel Request</DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="rounded-full bg-muted p-6 mb-4">
                  <ShareIcon className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium">No requests found</h3>
                <p className="text-muted-foreground mt-1 max-w-md">
                  You haven't sent any money requests yet, or no requests match your current filter.
                </p>
                <Button className="mt-6 gap-2">
                  <PlusIcon className="h-4 w-4" />
                  <span>Create a New Request</span>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  );
}
