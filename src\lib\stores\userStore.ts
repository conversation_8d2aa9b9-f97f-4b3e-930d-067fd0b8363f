import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: string
  name: string
  email: string
  phone?: string
  avatar?: string
  userType: 'personal' | 'business' | 'agent' | 'admin'
  kycLevel: 'basic' | 'enhanced' | 'verified'
  status: 'active' | 'pending' | 'suspended' | 'locked'
  businessInfo?: {
    businessName: string
    businessType: string
    registrationNumber?: string
    taxNumber?: string
  }
  agentInfo?: {
    agentCode: string
    territory: string
    commissionRate: number
  }
  preferences: {
    theme: 'light' | 'dark' | 'system'
    notifications: boolean
    language: string
    currency: 'USD' | 'ZWL'
    biometricEnabled: boolean
  }
  permissions: string[]
  lastLogin?: Date
  createdAt: Date
}

interface UserState {
  user: User | null
  isAuthenticated: boolean
  sessionToken: string | null
  setUser: (user: User | null) => void
  updateUser: (updates: Partial<User>) => void
  updatePreferences: (preferences: Partial<User['preferences']>) => void
  login: (user: User, token: string) => void
  logout: () => void
  updateKycLevel: (level: 'basic' | 'enhanced' | 'verified') => void
  updateStatus: (status: 'active' | 'pending' | 'suspended' | 'locked') => void
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      sessionToken: null,
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      updateUser: (updates) =>
        set((state) => ({
          user: state.user ? { ...state.user, ...updates } : null,
        })),
      updatePreferences: (preferences) =>
        set((state) => ({
          user: state.user
            ? {
                ...state.user,
                preferences: { ...state.user.preferences, ...preferences },
              }
            : null,
        })),
      login: (user, token) => set({
        user: { ...user, lastLogin: new Date() },
        isAuthenticated: true,
        sessionToken: token
      }),
      logout: () => set({
        user: null,
        isAuthenticated: false,
        sessionToken: null
      }),
      updateKycLevel: (level) =>
        set((state) => ({
          user: state.user ? { ...state.user, kycLevel: level } : null,
        })),
      updateStatus: (status) =>
        set((state) => ({
          user: state.user ? { ...state.user, status } : null,
        })),
    }),
    {
      name: 'user-storage',
    }
  )
)