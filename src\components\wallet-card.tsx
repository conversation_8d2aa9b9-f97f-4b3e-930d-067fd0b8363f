"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface WalletCardProps {
  name: string;
  balances: {
    ZWL: number;
    USD: number;
  };
  primaryCurrency: string;
  logo: string;
  logoAlt: string;
  isConnected?: boolean;
  isPrimary?: boolean;
}

export function WalletCard({
  name,
  balances,
  primaryCurrency,
  logo,
  logoAlt,
  isConnected = true,
  isPrimary = false
}: WalletCardProps) {
  // Special styling for ZB UniWallet
  if (isPrimary && name === "ZB UniWallet") {
    return (
      <Card className="wallet-card bg-gradient-to-br from-zb-green to-zb-lime">
        <div className="flex items-start justify-between">
          <div>
            <div className="flex items-center gap-2">
              <Image
                src={logo}
                alt={logoAlt}
                width={24}
                height={24}
                className="h-6 w-6 rounded-md bg-white/10 p-1"
              />
              <h3 className="text-sm font-medium text-white">{name}</h3>
            </div>
            <Tabs defaultValue={primaryCurrency} className="mt-2 w-full">
              <TabsList className="grid w-36 grid-cols-2 bg-white/10">
                <TabsTrigger value="USD" className="text-white data-[state=active]:bg-white/20">
                  USD
                </TabsTrigger>
                <TabsTrigger value="ZWL" className="text-white data-[state=active]:bg-white/20">
                  ZWL
                </TabsTrigger>
              </TabsList>
              <TabsContent value="USD" className="mt-2 p-0">
                <p className="text-2xl font-bold">
                  USD {balances.USD.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </p>
              </TabsContent>
              <TabsContent value="ZWL" className="mt-2 p-0">
                <p className="text-2xl font-bold">
                  ZWL {balances.ZWL.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </p>
              </TabsContent>
            </Tabs>
          </div>
          <div>
            <Badge variant="outline" className="border-white/30 bg-white/10 text-white">
              Connected
            </Badge>
          </div>
        </div>
        <div className="mt-6 grid grid-cols-3 gap-2">
          <button className="rounded-md bg-white/10 px-2 py-1 text-xs font-medium text-white hover:bg-white/20">
            Send
          </button>
          <button className="rounded-md bg-white/10 px-2 py-1 text-xs font-medium text-white hover:bg-white/20">
            Receive
          </button>
          <button className="rounded-md bg-white/10 px-2 py-1 text-xs font-medium text-white hover:bg-white/20">
            Top-up
          </button>
        </div>
      </Card>
    );
  }

  // Regular wallet card styling
  return (
    <Card className={`wallet-card ${name === "EcoCash"
      ? "bg-green-600"
      : name === "OneMoney"
        ? "bg-teal-600"
        : name === "InnBucks"
          ? "bg-blue-600"
          : name === "O'mari"
            ? "bg-purple-600"
            : "bg-gradient-to-br from-primary to-primary/80"}`}>
      <div className="flex items-start justify-between">
        <div>
          <h3 className="text-sm font-medium text-white/90">{name}</h3>
          <Tabs defaultValue={primaryCurrency} className="mt-2 w-full">
            <TabsList className="grid w-36 grid-cols-2 bg-white/10">
              <TabsTrigger value="ZWL" className="text-white data-[state=active]:bg-white/20">
                ZWL
              </TabsTrigger>
              <TabsTrigger value="USD" className="text-white data-[state=active]:bg-white/20">
                USD
              </TabsTrigger>
            </TabsList>
            <TabsContent value="ZWL" className="mt-2 p-0">
              <p className="text-2xl font-bold">
                ZWL {balances.ZWL.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
            </TabsContent>
            <TabsContent value="USD" className="mt-2 p-0">
              <p className="text-2xl font-bold">
                USD {balances.USD.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
            </TabsContent>
          </Tabs>
        </div>
        <div className="flex flex-col items-end gap-2">
          <Image
            src={logo}
            alt={logoAlt}
            width={40}
            height={40}
            className="h-10 w-10 rounded-md bg-white/10 p-1"
          />
          {isConnected ? (
            <Badge variant="outline" className="border-green-300 bg-green-900/20 text-green-300">
              Connected
            </Badge>
          ) : (
            <Badge variant="outline" className="border-yellow-300 bg-yellow-900/20 text-yellow-300">
              Not Connected
            </Badge>
          )}
        </div>
      </div>
      <div className="mt-6 grid grid-cols-3 gap-2">
        <button className="rounded-md bg-white/10 px-2 py-1 text-xs font-medium text-white hover:bg-white/20">
          Send
        </button>
        <button className="rounded-md bg-white/10 px-2 py-1 text-xs font-medium text-white hover:bg-white/20">
          Receive
        </button>
        <button className="rounded-md bg-white/10 px-2 py-1 text-xs font-medium text-white hover:bg-white/20">
          Top-up
        </button>
      </div>
    </Card>
  );
}
