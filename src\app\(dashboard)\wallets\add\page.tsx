'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/components/ui/use-toast'
import { useWalletStore } from '@/lib/stores/wallet-store'
import { motion } from 'framer-motion'

const mobileWallets = [
  { id: 'ecocash', name: 'EcoCash', logo: '/ecocash-logo.png' },
  { id: 'onemoney', name: 'OneMoney', logo: '/onemoney-logo.png' },
  { id: 'telecash', name: 'Telecash', logo: '/telecash-logo.png' },
  { id: 'omari', name: '<PERSON><PERSON>', logo: '/omari-logo.png' },
  { id: 'inbucks', name: 'InBucks', logo: '/inbucks-logo.png' },
  { id: 'zbuniwallet', name: 'ZBUniWallet', logo: '/zbuniwallet-logo.png' }
]

const banks = [
  { id: 'zb', name: 'ZB Bank', logo: '/zb-bank-logo.png' },
  { id: 'cbz', name: 'CBZ Bank', logo: '/cbz-logo.png' },
  { id: 'stanbic', name: 'Stanbic Bank', logo: '/stanbic-logo.png' },
  { id: 'fnb', name: 'FNB Zimbabwe', logo: '/fnb-logo.png' },
  { id: 'nedbank', name: 'Nedbank Zimbabwe', logo: '/nedbank-logo.png' },
  { id: 'cabs', name: 'CABS', logo: '/cabs-logo.png' },
  { id: 'steward', name: 'Steward Bank', logo: '/steward-logo.png' },
  { id: 'nmb', name: 'NMB Bank', logo: '/nmb-logo.png' }
]

export default function AddWalletPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { addWallet } = useWalletStore()
  const [walletType, setWalletType] = useState<'mobile' | 'bank'>('mobile')
  const [selectedProvider, setSelectedProvider] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    accountNumber: '',
    phoneNumber: '',
    branch: '',
    logoAlt: '',
    primaryCurrency: 'USD' as 'USD' | 'ZWL'
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const provider = walletType === 'mobile' 
        ? mobileWallets.find(w => w.id === selectedProvider)
        : banks.find(b => b.id === selectedProvider)

      if (!provider) {
        throw new Error('Please select a provider')
      }

      // Create a new wallet object
      const newWallet = {
        name: formData.name,
        type: walletType,
        state: 'pending' as const,
        balances: {
          USD: 0,
          ZWL: 0
        },
        logo: provider.logo,
        logoAlt: provider.name,
        primaryCurrency: formData.primaryCurrency,
        accountNumber: formData.accountNumber,
        phoneNumber: formData.phoneNumber,
        branch: formData.branch
      }

      // Add the wallet to the store
      addWallet(newWallet)

      // Clear form
      setWalletType('mobile')
      setSelectedProvider('')
      setFormData({
        name: '',
        accountNumber: '',
        phoneNumber: '',
        branch: '',
        logoAlt: '',
        primaryCurrency: 'USD' as 'USD' | 'ZWL'
      })

      toast({
        title: 'Success!',
        description: 'Your wallet has been added and is pending verification.',
      })

      // Navigate back to wallets page
      router.push('/wallets')
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to add wallet. Please try again.',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="max-w-2xl mx-auto py-4 sm:py-6 px-4 sm:px-0">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader className="space-y-2 sm:space-y-3">
            <CardTitle className="text-xl sm:text-2xl">Add New Wallet</CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Connect a new mobile money wallet or bank account to your ZB Digital Wallet
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4 sm:space-y-6">
              <div className="space-y-2">
                <Label className="text-sm sm:text-base">Wallet Type</Label>
                <RadioGroup
                  value={walletType}
                  onValueChange={(value: 'mobile' | 'bank') => {
                    setWalletType(value)
                    setSelectedProvider('')
                    setFormData(prev => ({
                      ...prev,
                      name: '',
                      accountNumber: '',
                      phoneNumber: '',
                      branch: ''
                    }))
                  }}
                  className="flex flex-col sm:flex-row gap-2 sm:gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="mobile" id="mobile" />
                    <Label htmlFor="mobile" className="text-sm sm:text-base">Mobile Money</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="bank" id="bank" />
                    <Label htmlFor="bank" className="text-sm sm:text-base">Bank Account</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label className="text-sm sm:text-base">
                  {walletType === 'mobile' ? 'Select Mobile Money Provider' : 'Select Bank'}
                </Label>
                <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={walletType === 'mobile' ? "Select mobile money provider" : "Select bank"} />
                  </SelectTrigger>
                  <SelectContent>
                    {(walletType === 'mobile' ? mobileWallets : banks).map((provider) => (
                      <SelectItem key={provider.id} value={provider.id}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm sm:text-base">
                  {walletType === 'mobile' ? 'Wallet Name' : 'Account Name'}
                </Label>
                <Input
                  id="name"
                  placeholder={walletType === 'mobile' ? 'e.g. My EcoCash Wallet' : 'e.g. My ZB Account'}
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                  className="w-full"
                />
              </div>

              {walletType === 'mobile' ? (
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber" className="text-sm sm:text-base">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    placeholder="Enter your mobile money number"
                    value={formData.phoneNumber}
                    onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
                    required
                    className="w-full"
                  />
                </div>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="accountNumber" className="text-sm sm:text-base">Account Number</Label>
                    <Input
                      id="accountNumber"
                      placeholder="Enter your bank account number"
                      value={formData.accountNumber}
                      onChange={(e) => setFormData({ ...formData, accountNumber: e.target.value })}
                      required
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="branch" className="text-sm sm:text-base">Branch</Label>
                    <Input
                      id="branch"
                      placeholder="Enter bank branch"
                      value={formData.branch}
                      onChange={(e) => setFormData({ ...formData, branch: e.target.value })}
                      required
                      className="w-full"
                    />
                  </div>
                </>
              )}

              <div className="space-y-2">
                <Label htmlFor="primaryCurrency" className="text-sm sm:text-base">Primary Currency</Label>
                <Select value={formData.primaryCurrency} onValueChange={(value: 'USD' | 'ZWL') => setFormData({ ...formData, primaryCurrency: value })}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select primary currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="ZWL">ZWL</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row justify-between gap-2 sm:gap-0">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button type="submit" className="w-full sm:w-auto">Add Wallet</Button>
            </CardFooter>
          </form>
        </Card>
      </motion.div>
    </div>
  )
} 