"use client"

import { Building2 } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { DashboardShell } from "@/components/dashboard-shell"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"

export default function BusinessToolsPage() {
  return (
    <DashboardShell>
      <Card>
        <CardHeader>
          <CardTitle>Business Tools</CardTitle>
          <CardDescription>Access tools and features for your business</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="container max-w-5xl py-6 space-y-8">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <Building2 className="h-16 w-16 text-primary" />
              <h1 className="text-3xl font-bold">Business Tools</h1>
              <p className="text-muted-foreground max-w-[600px]">
                Powerful tools to help you manage and grow your business, including accounting, invoicing, inventory management, and more.
              </p>
            </div>

            <Alert className="bg-primary/10 border-primary">
              <Building2 className="h-4 w-4" />
              <AlertTitle>Coming Soon!</AlertTitle>
              <AlertDescription>
                We're working hard to bring you a comprehensive suite of business tools. Stay tuned for updates!
              </AlertDescription>
            </Alert>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[
                {
                  title: "Accounting & Tax",
                  description: "Manage your finances and tax compliance effortlessly",
                },
                {
                  title: "Invoicing",
                  description: "Create and manage professional invoices",
                },
                {
                  title: "Inventory Management",
                  description: "Track and manage your stock efficiently",
                },
                {
                  title: "Payroll",
                  description: "Handle employee payments and records",
                },
                {
                  title: "Reports & Analytics",
                  description: "Get insights into your business performance",
                },
                {
                  title: "Customer Management",
                  description: "Manage your customer relationships",
                },
              ].map((tool) => (
                <div
                  key={tool.title}
                  className="p-6 rounded-lg border bg-card text-card-foreground shadow-sm opacity-75"
                >
                  <h3 className="font-semibold">{tool.title}</h3>
                  <p className="text-sm text-muted-foreground mt-2">{tool.description}</p>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  )
} 