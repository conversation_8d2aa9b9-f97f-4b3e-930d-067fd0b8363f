'use client'

import { useState } from 'react'
import { useWalletStore, type Wallet, type WalletType } from '@/lib/stores/wallet-store'
import { useToast } from '@/components/ui/use-toast'

export function AddWalletForm() {
  const { addWallet } = useWalletStore()
  const { toast } = useToast()
  const [formData, setFormData] = useState<Omit<Wallet, 'id' | 'isConnected'>>({
    name: '',
    logo: '/placeholder-logo.png',
    logoAlt: 'Wallet Logo',
    type: 'bank',
    state: 'connected',
    balances: {
      ZWL: 0,
      USD: 0
    },
    primaryCurrency: 'USD'
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      addWallet(formData)
      
      toast({
        title: "Success!",
        description: `Successfully added ${formData.name} wallet.`,
      })
      
      // Reset form
      setFormData({
        name: '',
        logo: '/placeholder-logo.png',
        logoAlt: 'Wallet Logo',
        type: 'bank',
        state: 'connected',
        balances: {
          ZWL: 0,
          USD: 0
        },
        primaryCurrency: 'USD'
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add wallet. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 p-4 border rounded-lg">
      <h3 className="text-lg font-semibold">Add New Wallet</h3>
      <div>
        <label className="block text-sm font-medium mb-1">Wallet Name</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full p-2 border rounded"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Type</label>
        <select
          value={formData.type}
          onChange={(e) => setFormData({ ...formData, type: e.target.value as WalletType })}
          className="w-full p-2 border rounded"
        >
          <option value="mobile">Mobile Money</option>
          <option value="bank">Bank Account</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Primary Currency</label>
        <select
          value={formData.primaryCurrency}
          onChange={(e) => setFormData({ ...formData, primaryCurrency: e.target.value as "ZWL" | "USD" })}
          className="w-full p-2 border rounded"
        >
          <option value="USD">USD</option>
          <option value="ZWL">ZWL</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Initial Balance (USD)</label>
        <input
          type="number"
          value={formData.balances.USD}
          onChange={(e) => setFormData({
            ...formData,
            balances: {
              ...formData.balances,
              USD: Number(e.target.value)
            }
          })}
          className="w-full p-2 border rounded"
          min="0"
          step="0.01"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Initial Balance (ZWL)</label>
        <input
          type="number"
          value={formData.balances.ZWL}
          onChange={(e) => setFormData({
            ...formData,
            balances: {
              ...formData.balances,
              ZWL: Number(e.target.value)
            }
          })}
          className="w-full p-2 border rounded"
          min="0"
          step="0.01"
          required
        />
      </div>

      <button
        type="submit"
        className="w-full bg-primary text-primary-foreground hover:bg-primary/90 py-2 px-4 rounded font-medium transition-colors duration-200"
      >
        Add Wallet
      </button>
    </form>
  )
} 