'use client'

import { usePathname, useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { 
  Home,
  Wallet,
  SendHorizontal,
  Receipt,
  History
} from 'lucide-react'

export function MobileNav() {
  const pathname = usePathname()
  const router = useRouter()

  const navigation = [
    {
      name: 'Home',
      href: '/',
      icon: Home
    },
    {
      name: 'Wallets',
      href: '/wallets',
      icon: Wallet
    },
    {
      name: 'Send',
      href: '/send-money',
      icon: SendHorizontal
    },
    {
      name: 'Bills',
      href: '/pay-bills',
      icon: Receipt
    },
    {
      name: 'History',
      href: '/transactions',
      icon: History
    }
  ]

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 md:hidden">
      <nav className="h-16 bg-background border-t flex items-center justify-around px-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <button
              key={item.name}
              onClick={() => router.push(item.href)}
              className={cn(
                'flex flex-col items-center justify-center gap-1 p-2 rounded-lg transition-colors',
                isActive ? 'text-primary' : 'text-muted-foreground hover:text-primary'
              )}
            >
              <item.icon className="h-5 w-5" />
              <span className="text-xs font-medium">{item.name}</span>
            </button>
          )
        })}
      </nav>
    </div>
  )
} 