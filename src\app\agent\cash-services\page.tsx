"use client";

import { useState } from 'react'
import { DashboardShell } from '@/components/dashboard-shell'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ArrowDownIcon, 
  ArrowUpIcon, 
  Wallet, 
  User, 
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  Smartphone,
  CreditCard
} from 'lucide-react'

export default function CashServicesPage() {
  const [activeTab, setActiveTab] = useState('cash-in')
  const [customerPhone, setCustomerPhone] = useState('')
  const [amount, setAmount] = useState('')
  const [customerPin, setCustomerPin] = useState('')
  const [agentPin, setAgentPin] = useState('')
  const [selectedWallet, setSelectedWallet] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  // Mock agent data
  const agentData = {
    floatBalance: 25000,
    dailyLimit: 50000,
    transactionLimit: 5000,
    commission: {
      cashIn: 1.5, // percentage
      cashOut: 2.0 // percentage
    }
  }

  // Mock recent transactions
  const recentTransactions = [
    {
      id: 'TXN-001',
      type: 'cash_in',
      customer: '+263771234567',
      amount: 500,
      commission: 7.50,
      time: '10:30 AM',
      status: 'completed'
    },
    {
      id: 'TXN-002',
      type: 'cash_out',
      customer: '+263772345678',
      amount: 300,
      commission: 6.00,
      time: '09:45 AM',
      status: 'completed'
    },
    {
      id: 'TXN-003',
      type: 'cash_in',
      customer: '+263773456789',
      amount: 1000,
      commission: 15.00,
      time: '09:15 AM',
      status: 'completed'
    }
  ]

  const calculateCommission = (amount: number, type: 'cash_in' | 'cash_out') => {
    const rate = type === 'cash_in' ? agentData.commission.cashIn : agentData.commission.cashOut
    return (amount * rate) / 100
  }

  const handleTransaction = async (type: 'cash_in' | 'cash_out') => {
    if (!customerPhone || !amount || !customerPin || !agentPin || !selectedWallet) {
      alert('Please fill in all required fields')
      return
    }

    const transactionAmount = parseFloat(amount)
    
    if (type === 'cash_out' && transactionAmount > agentData.floatBalance) {
      alert('Insufficient float balance')
      return
    }

    if (transactionAmount > agentData.transactionLimit) {
      alert(`Transaction exceeds limit of USD ${agentData.transactionLimit.toLocaleString()}`)
      return
    }

    setIsProcessing(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const commission = calculateCommission(transactionAmount, type)
      
      alert(`Transaction successful!\nAmount: USD ${transactionAmount}\nCommission: USD ${commission.toFixed(2)}`)
      
      // Clear form
      setCustomerPhone('')
      setAmount('')
      setCustomerPin('')
      setAgentPin('')
      setSelectedWallet('')
      
    } catch (error) {
      alert('Transaction failed. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <DashboardShell>
      <div className="container px-4 sm:px-6 space-y-6 sm:space-y-8 pb-20 md:pb-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Cash Services</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Process cash-in and cash-out transactions for customers
            </p>
          </div>
        </div>

        {/* Agent Status */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Float Balance</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {agentData.floatBalance.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Available for cash-out</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Daily Limit</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {agentData.dailyLimit.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Transaction limit</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Commission Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{agentData.commission.cashIn}% - {agentData.commission.cashOut}%</div>
              <p className="text-xs text-muted-foreground">Cash-in / Cash-out</p>
            </CardContent>
          </Card>
        </div>

        {/* Transaction Processing */}
        <Card>
          <CardHeader>
            <CardTitle>Process Transaction</CardTitle>
            <CardDescription>Help customers with cash-in and cash-out services</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="cash-in" className="flex items-center gap-2">
                  <ArrowDownIcon className="h-4 w-4" />
                  Cash In
                </TabsTrigger>
                <TabsTrigger value="cash-out" className="flex items-center gap-2">
                  <ArrowUpIcon className="h-4 w-4" />
                  Cash Out
                </TabsTrigger>
              </TabsList>

              <TabsContent value="cash-in" className="space-y-4 mt-6">
                <Alert>
                  <ArrowDownIcon className="h-4 w-4" />
                  <AlertDescription>
                    Customer deposits cash with you, and you transfer digital money to their wallet.
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="customer-phone">Customer Phone Number</Label>
                    <Input
                      id="customer-phone"
                      placeholder="+263 77 123 4567"
                      value={customerPhone}
                      onChange={(e) => setCustomerPhone(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="wallet-type">Customer Wallet</Label>
                    <Select value={selectedWallet} onValueChange={setSelectedWallet}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select wallet type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ecocash">EcoCash</SelectItem>
                        <SelectItem value="onemoney">OneMoney</SelectItem>
                        <SelectItem value="innbucks">InnBucks</SelectItem>
                        <SelectItem value="omari">O'mari</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (USD)</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="100.00"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                    />
                    {amount && (
                      <p className="text-xs text-muted-foreground">
                        Commission: USD {calculateCommission(parseFloat(amount) || 0, 'cash_in').toFixed(2)}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customer-pin">Customer PIN</Label>
                    <Input
                      id="customer-pin"
                      type="password"
                      placeholder="••••"
                      maxLength={4}
                      value={customerPin}
                      onChange={(e) => setCustomerPin(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="agent-pin">Your Agent PIN</Label>
                    <Input
                      id="agent-pin"
                      type="password"
                      placeholder="••••••"
                      maxLength={6}
                      value={agentPin}
                      onChange={(e) => setAgentPin(e.target.value)}
                    />
                  </div>
                </div>

                <Button 
                  onClick={() => handleTransaction('cash_in')} 
                  disabled={isProcessing}
                  className="w-full"
                >
                  {isProcessing ? 'Processing...' : 'Process Cash In'}
                </Button>
              </TabsContent>

              <TabsContent value="cash-out" className="space-y-4 mt-6">
                <Alert>
                  <ArrowUpIcon className="h-4 w-4" />
                  <AlertDescription>
                    Customer requests cash withdrawal, and you deduct digital money from their wallet.
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="customer-phone-out">Customer Phone Number</Label>
                    <Input
                      id="customer-phone-out"
                      placeholder="+263 77 123 4567"
                      value={customerPhone}
                      onChange={(e) => setCustomerPhone(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="wallet-type-out">Customer Wallet</Label>
                    <Select value={selectedWallet} onValueChange={setSelectedWallet}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select wallet type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ecocash">EcoCash</SelectItem>
                        <SelectItem value="onemoney">OneMoney</SelectItem>
                        <SelectItem value="innbucks">InnBucks</SelectItem>
                        <SelectItem value="omari">O'mari</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="amount-out">Amount (USD)</Label>
                    <Input
                      id="amount-out"
                      type="number"
                      placeholder="100.00"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                    />
                    {amount && (
                      <div className="space-y-1">
                        <p className="text-xs text-muted-foreground">
                          Commission: USD {calculateCommission(parseFloat(amount) || 0, 'cash_out').toFixed(2)}
                        </p>
                        {parseFloat(amount) > agentData.floatBalance && (
                          <p className="text-xs text-red-600">
                            Insufficient float balance
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customer-pin-out">Customer PIN</Label>
                    <Input
                      id="customer-pin-out"
                      type="password"
                      placeholder="••••"
                      maxLength={4}
                      value={customerPin}
                      onChange={(e) => setCustomerPin(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="agent-pin-out">Your Agent PIN</Label>
                    <Input
                      id="agent-pin-out"
                      type="password"
                      placeholder="••••••"
                      maxLength={6}
                      value={agentPin}
                      onChange={(e) => setAgentPin(e.target.value)}
                    />
                  </div>
                </div>

                <Button 
                  onClick={() => handleTransaction('cash_out')} 
                  disabled={isProcessing || (amount && parseFloat(amount) > agentData.floatBalance)}
                  className="w-full"
                >
                  {isProcessing ? 'Processing...' : 'Process Cash Out'}
                </Button>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>Your latest cash service transactions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      transaction.type === 'cash_in' 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-blue-100 text-blue-600'
                    }`}>
                      {transaction.type === 'cash_in' ? (
                        <ArrowDownIcon className="h-4 w-4" />
                      ) : (
                        <ArrowUpIcon className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">
                        {transaction.type === 'cash_in' ? 'Cash In' : 'Cash Out'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {transaction.customer} • {transaction.time}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">USD {transaction.amount}</p>
                    <p className="text-sm text-green-600">+USD {transaction.commission} commission</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
