"use client";

import Link from 'next/link';
import Image from 'next/image';
import {
  HomeIcon,
  WalletIcon,
  SendIcon,
  DollarSignIcon,
  BarChart3Icon,
  SettingsIcon,
  HelpCircleIcon,
  LogOutIcon,
  PlusIcon,
  BanknoteIcon,
  CreditCardIcon as CardIcon,
  ArrowDownToLineIcon,
  ArrowUpFromLineIcon,
  LayoutDashboard,
  Wallet,
  Receipt,
  History,
  LogOut,
  Plus,
  Globe2,
  QrCode
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePathname } from 'next/navigation';
import { cn } from "@/lib/utils";

const navItems = [
  { name: 'Dashboard', href: '/', icon: LayoutDashboard },
  { name: 'Wallets', href: '/wallets', icon: Wallet },
  { name: 'Send Money', href: '/send-money', icon: SendIcon },
  { name: 'Pay Bills', href: '/pay-bills', icon: Receipt },
  { name: '<PERSON><PERSON> QR', href: '/scan-qr', icon: QrCode },
  { name: 'International Remittances', href: '/international-remittances', icon: Globe2 },
  { name: 'My Cards', href: '/cards', icon: CardIcon },
  { name: 'Sent Requests', href: '/sent-requests', icon: ArrowUpFromLineIcon },
  { name: 'Received Requests', href: '/received-requests', icon: ArrowDownToLineIcon },
  { name: 'Transactions', href: '/transactions', icon: History },
  { name: 'Settings', href: '/settings', icon: SettingsIcon },
  { name: 'Help & Support', href: '/support', icon: HelpCircleIcon }
];

export function Sidebar() {
  const pathname = usePathname();

  return (
    <div className="flex h-full w-full flex-col border-r bg-background">
      <div className="flex h-16 items-center gap-2 border-b px-4">
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/images/zb-logo.png"
            alt="ZB Digital Wallet"
            width={40}
            height={40}
            className="h-8 w-auto"
          />
          <span className="font-semibold">ZB Digital Wallet</span>
        </Link>
      </div>
      <Button
        className="mx-4 my-4 gap-2"
        size="sm"
      >
        <Plus className="h-4 w-4" />
        Add New Wallet
      </Button>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid items-start px-2 text-sm font-medium">
          {navItems.map((item) => {
            const isActive =
              item.href === '/'
                ? pathname === '/'
                : pathname.startsWith(item.href);

            return (
              <Link
                key={item.name}
                href={item.href}
                className={`nav-link ${
                  isActive
                    ? 'bg-zb-green/10 text-zb-green font-medium'
                    : 'text-foreground hover:bg-muted'
                }`}
              >
                <item.icon className={`h-4 w-4 ${isActive ? 'text-zb-green' : ''}`} />
                {item.name}
              </Link>
            );
          })}
        </nav>
      </div>
      <div className="mt-auto border-t p-4">
        <Button variant="ghost" className="justify-start gap-2 text-red-500 hover:text-red-600 hover:bg-red-50">
          <LogOut className="h-4 w-4" />
          Log Out
        </Button>
      </div>
    </div>
  );
}
