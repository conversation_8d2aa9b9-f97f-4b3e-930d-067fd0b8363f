"use client";

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardShell } from '@/components/dashboard-shell'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Wallet, 
  Users, 
  DollarSign, 
  TrendingUp, 
  MapPin, 
  Clock,
  ArrowUpIcon,
  ArrowDownIcon,
  UserPlus,
  Banknote
} from 'lucide-react'

export default function AgentDashboard() {
  const router = useRouter()

  // Mock agent data
  const agentMetrics = {
    floatBalance: 25000,
    dailyCommissions: 125.50,
    monthlyCommissions: 2850.75,
    customersServed: 156,
    cashInToday: 15000,
    cashOutToday: 8500,
    territory: "Harare Central",
    agentCode: "AG001234"
  }

  const recentTransactions = [
    {
      id: 1,
      type: "cash_in",
      customer: "<PERSON> (+************)",
      amount: 500,
      commission: 5.00,
      time: "2 minutes ago",
      status: "completed"
    },
    {
      id: 2,
      type: "cash_out",
      customer: "<PERSON> (+************)",
      amount: 300,
      commission: 3.00,
      time: "15 minutes ago",
      status: "completed"
    },
    {
      id: 3,
      type: "customer_registration",
      customer: "Mike Johnson (+************)",
      amount: 0,
      commission: 10.00,
      time: "1 hour ago",
      status: "completed"
    },
    {
      id: 4,
      type: "cash_in",
      customer: "Sarah Wilson (+263774567890)",
      amount: 1000,
      commission: 10.00,
      time: "2 hours ago",
      status: "completed"
    }
  ]

  const quickActions = [
    {
      title: "Cash In",
      description: "Process customer cash deposit",
      icon: ArrowDownIcon,
      href: "/agent/cash-in",
      color: "bg-green-500"
    },
    {
      title: "Cash Out",
      description: "Process customer cash withdrawal",
      icon: ArrowUpIcon,
      href: "/agent/cash-out",
      color: "bg-blue-500"
    },
    {
      title: "Register Customer",
      description: "Onboard new customer",
      icon: UserPlus,
      href: "/agent/register-customer",
      color: "bg-purple-500"
    },
    {
      title: "Float Top-up",
      description: "Request float balance increase",
      icon: Banknote,
      href: "/agent/float-topup",
      color: "bg-orange-500"
    }
  ]

  return (
    <DashboardShell>
      <div className="container px-4 sm:px-6 space-y-6 sm:space-y-8 pb-20 md:pb-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Agent Dashboard</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Agent Code: {agentMetrics.agentCode} • {agentMetrics.territory}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-green-600 border-green-600">
              <div className="w-2 h-2 bg-green-600 rounded-full mr-2"></div>
              Active
            </Badge>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Float Balance</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {agentMetrics.floatBalance.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Available for transactions</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today's Commissions</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {agentMetrics.dailyCommissions}</div>
              <p className="text-xs text-muted-foreground">Earned today</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Commissions</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {agentMetrics.monthlyCommissions}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customers Served</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{agentMetrics.customersServed}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>
        </div>

        {/* Today's Activity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Cash In Today</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-600">USD {agentMetrics.cashInToday.toLocaleString()}</div>
              <Progress value={75} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-1">75% of daily target</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Cash Out Today</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600">USD {agentMetrics.cashOutToday.toLocaleString()}</div>
              <Progress value={60} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-1">60% of daily target</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Card 
                key={index} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => router.push(action.href)}
              >
                <CardContent className="p-4 flex flex-col items-center text-center space-y-2">
                  <div className={`p-3 rounded-full ${action.color} text-white`}>
                    <action.icon className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold">{action.title}</h3>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <h2 className="text-lg sm:text-xl font-semibold">Recent Activity</h2>
            <Button variant="outline" size="sm" onClick={() => router.push('/agent/transactions')}>
              View All
            </Button>
          </div>
          <div className="space-y-3">
            {recentTransactions.map((transaction) => (
              <Card key={transaction.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-full ${
                        transaction.type === "cash_in" 
                          ? "bg-green-100 text-green-600" 
                          : transaction.type === "cash_out"
                          ? "bg-blue-100 text-blue-600"
                          : "bg-purple-100 text-purple-600"
                      }`}>
                        {transaction.type === "cash_in" ? (
                          <ArrowDownIcon className="h-4 w-4" />
                        ) : transaction.type === "cash_out" ? (
                          <ArrowUpIcon className="h-4 w-4" />
                        ) : (
                          <UserPlus className="h-4 w-4" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {transaction.type === "cash_in" ? "Cash In" : 
                           transaction.type === "cash_out" ? "Cash Out" : "Customer Registration"}
                        </p>
                        <p className="text-sm text-muted-foreground">{transaction.customer}</p>
                        <p className="text-xs text-muted-foreground">{transaction.time}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      {transaction.amount > 0 && (
                        <p className="font-medium">USD {transaction.amount.toLocaleString()}</p>
                      )}
                      <p className="text-sm text-green-600">+USD {transaction.commission} commission</p>
                      <Badge 
                        variant={transaction.status === "completed" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {transaction.status}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </DashboardShell>
  )
}
