"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { <PERSON><PERSON>, Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface VirtualCardProps {
  id: string;
  cardNumber: string;
  expiryDate: string;
  cardholderName: string;
  cvv: string;
  cardType: "visa" | "mastercard" | "uniCard";
  status: "active" | "inactive" | "blocked";
  balances: {
    ZWL: number;
    USD: number;
  };
  primaryCurrency: string;
  features?: string[];
  cardColor?: string;
  cardImage?: string;
}

export function VirtualCard({
  id,
  cardNumber,
  expiryDate,
  cardholderName,
  cvv,
  cardType,
  status,
  balances,
  primaryCurrency,
  features,
  cardColor,
  cardImage
}: VirtualCardProps) {
  const [showCardDetails, setShowCardDetails] = useState(false);

  const maskedCardNumber = showCardDetails
    ? cardNumber.match(/.{1,4}/g)?.join(' ')
    : "•••• •••• •••• " + cardNumber.slice(-4);

  const maskedCvv = showCardDetails ? cvv : "•••";

  const toggleCardDetails = () => {
    setShowCardDetails(!showCardDetails);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const getCardBackground = () => {
    if (cardColor) return cardColor;

    if (cardType === "visa") return "bg-gradient-to-br from-blue-500 to-blue-700";
    if (cardType === "mastercard") return "bg-gradient-to-br from-orange-500 to-red-700";

    return "bg-gradient-to-br from-zb-green to-zb-lime";
  };

  return (
    <Card className="relative overflow-hidden">
      <div className={`w-full h-56 p-6 rounded-lg flex flex-col justify-between ${getCardBackground()}`}>
        <div className="flex justify-between items-start">
          <div>
            <Badge
              variant="outline"
              className={`${status === "active"
                ? "bg-green-500/20 text-green-100 border-green-300"
                : status === "inactive"
                  ? "bg-yellow-500/20 text-yellow-100 border-yellow-300"
                  : "bg-red-500/20 text-red-100 border-red-300"
              }`}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
          </div>
          <div className="h-12 w-auto">
            {cardType === "visa" ? (
              <Image
                src="/images/visa-logo.png"
                alt="Visa"
                width={60}
                height={40}
                className="h-10 w-auto object-contain"
              />
            ) : cardType === "mastercard" ? (
              <Image
                src="/images/mastercard-logo.png"
                alt="Mastercard"
                width={60}
                height={40}
                className="h-10 w-auto object-contain"
              />
            ) : (
              <Image
                src={cardImage || "/images/zb-logo.png"}
                alt="ZB UniCard"
                width={60}
                height={40}
                className="h-10 w-auto object-contain bg-white/10 rounded-md p-1"
              />
            )}
          </div>
        </div>

        <div className="w-full mt-4">
          <div className="text-white text-xl font-medium mb-1 flex items-center justify-between">
            <span>{maskedCardNumber}</span>
            <button
              onClick={toggleCardDetails}
              className="p-2 rounded-full hover:bg-white/10"
            >
              {showCardDetails ? (
                <EyeOff className="h-5 w-5 text-white" />
              ) : (
                <Eye className="h-5 w-5 text-white" />
              )}
            </button>
          </div>

          <div className="grid grid-cols-2 gap-4 text-white/80">
            <div>
              <p className="text-xs uppercase">Card Holder</p>
              <p className="font-medium">{cardholderName}</p>
            </div>
            <div className="flex gap-4">
              <div>
                <p className="text-xs uppercase">Expires</p>
                <p className="font-medium">{expiryDate}</p>
              </div>
              <div>
                <p className="text-xs uppercase">CVV</p>
                <p className="font-medium flex items-center gap-1">
                  {maskedCvv}
                  <button
                    onClick={() => copyToClipboard(cvv)}
                    className="p-1 rounded-full hover:bg-white/10"
                  >
                    <Copy className="h-3 w-3 text-white/70" />
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="absolute bottom-4 right-6 text-white">
          <p className="text-sm text-white/80">Available Balance</p>
          <Tabs defaultValue={primaryCurrency} className="w-full">
            <TabsList className="grid w-32 grid-cols-2 bg-white/10">
              <TabsTrigger
                value="ZWL"
                className="text-xs text-white data-[state=active]:bg-white/20 py-0.5 h-7"
              >
                ZWL
              </TabsTrigger>
              <TabsTrigger
                value="USD"
                className="text-xs text-white data-[state=active]:bg-white/20 py-0.5 h-7"
              >
                USD
              </TabsTrigger>
            </TabsList>
            <TabsContent value="ZWL" className="p-0 mt-1">
              <p className="text-xl font-bold">
                ZWL {balances.ZWL.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </p>
            </TabsContent>
            <TabsContent value="USD" className="p-0 mt-1">
              <p className="text-xl font-bold">
                USD {balances.USD.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </p>
            </TabsContent>
          </Tabs>
        </div>

        {features && features.length > 0 && (
          <div className="absolute bottom-4 left-6 text-white/90 max-w-[60%]">
            <div className="text-xs space-y-1">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <div className="h-1 w-1 rounded-full bg-white/80 mr-1.5"></div>
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="p-4 flex gap-2 border-t">
        <Button variant="outline" size="sm" className="flex-1">Top Up</Button>
        <Button variant="outline" size="sm" className="flex-1">Freeze Card</Button>
        <Button variant="outline" size="sm" className="flex-1">Settings</Button>
      </div>
    </Card>
  );
}
