"use client";

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardShell } from '@/components/dashboard-shell'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Building2, 
  Users, 
  DollarSign, 
  TrendingUp, 
  FileText, 
  CreditCard,
  ArrowUpIcon,
  ArrowDownIcon,
  MoreHorizontal,
  Plus
} from 'lucide-react'

export default function BusinessDashboard() {
  const router = useRouter()

  // Mock business data
  const businessMetrics = {
    totalRevenue: 125000,
    monthlyGrowth: 12.5,
    activeEmployees: 45,
    pendingInvoices: 8,
    accountBalance: 85000,
    monthlyTransactions: 234
  }

  const recentTransactions = [
    {
      id: 1,
      type: "bulk_payment",
      description: "Monthly Payroll - October 2024",
      amount: 45000,
      date: "Today, 9:00 AM",
      status: "completed",
      employees: 45
    },
    {
      id: 2,
      type: "invoice_payment",
      description: "Payment from ABC Corp - Invoice #INV-001",
      amount: 15000,
      date: "Yesterday, 2:30 PM",
      status: "completed"
    },
    {
      id: 3,
      type: "vendor_payment",
      description: "Office Supplies - XYZ Suppliers",
      amount: -2500,
      date: "Oct 28, 11:15 AM",
      status: "completed"
    },
    {
      id: 4,
      type: "bulk_payment",
      description: "Vendor Payments Batch #BP-102",
      amount: -8500,
      date: "Oct 27, 3:45 PM",
      status: "pending",
      vendors: 12
    }
  ]

  const quickActions = [
    {
      title: "Process Payroll",
      description: "Run monthly payroll for all employees",
      icon: Users,
      href: "/business/payroll",
      color: "bg-blue-500"
    },
    {
      title: "Create Invoice",
      description: "Generate new customer invoice",
      icon: FileText,
      href: "/business/invoices/create",
      color: "bg-green-500"
    },
    {
      title: "Bulk Payment",
      description: "Process multiple payments at once",
      icon: CreditCard,
      href: "/business/bulk-payments",
      color: "bg-purple-500"
    },
    {
      title: "Add Team Member",
      description: "Invite new team member",
      icon: Plus,
      href: "/business/team/invite",
      color: "bg-orange-500"
    }
  ]

  return (
    <DashboardShell>
      <div className="container px-4 sm:px-6 space-y-6 sm:space-y-8 pb-20 md:pb-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Business Dashboard</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage your business finances and operations
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-green-600 border-green-600">
              <div className="w-2 h-2 bg-green-600 rounded-full mr-2"></div>
              Active
            </Badge>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {businessMetrics.totalRevenue.toLocaleString()}</div>
              <div className="flex items-center text-xs text-green-600">
                <ArrowUpIcon className="h-3 w-3 mr-1" />
                +{businessMetrics.monthlyGrowth}% from last month
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Account Balance</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {businessMetrics.accountBalance.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Available funds</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Employees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{businessMetrics.activeEmployees}</div>
              <p className="text-xs text-muted-foreground">Team members</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Invoices</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{businessMetrics.pendingInvoices}</div>
              <p className="text-xs text-muted-foreground">Awaiting payment</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Card 
                key={index} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => router.push(action.href)}
              >
                <CardContent className="p-4 flex flex-col items-center text-center space-y-2">
                  <div className={`p-3 rounded-full ${action.color} text-white`}>
                    <action.icon className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold">{action.title}</h3>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Recent Business Transactions */}
        <div>
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <h2 className="text-lg sm:text-xl font-semibold">Recent Business Activity</h2>
            <Button variant="outline" size="sm" onClick={() => router.push('/business/transactions')}>
              View All
            </Button>
          </div>
          <div className="space-y-3">
            {recentTransactions.map((transaction) => (
              <Card key={transaction.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-full ${
                        transaction.type === "invoice_payment" 
                          ? "bg-green-100 text-green-600" 
                          : transaction.amount > 0
                          ? "bg-blue-100 text-blue-600"
                          : "bg-orange-100 text-orange-600"
                      }`}>
                        {transaction.type === "invoice_payment" ? (
                          <ArrowDownIcon className="h-4 w-4" />
                        ) : transaction.amount > 0 ? (
                          <ArrowDownIcon className="h-4 w-4" />
                        ) : (
                          <ArrowUpIcon className="h-4 w-4" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">{transaction.description}</p>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <span>{transaction.date}</span>
                          {transaction.employees && (
                            <span>• {transaction.employees} employees</span>
                          )}
                          {transaction.vendors && (
                            <span>• {transaction.vendors} vendors</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${
                        transaction.amount > 0 
                          ? "text-green-600" 
                          : "text-red-600"
                      }`}>
                        {transaction.amount > 0 ? "+" : ""}USD {Math.abs(transaction.amount).toLocaleString()}
                      </p>
                      <Badge 
                        variant={transaction.status === "completed" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {transaction.status}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </DashboardShell>
  )
}
