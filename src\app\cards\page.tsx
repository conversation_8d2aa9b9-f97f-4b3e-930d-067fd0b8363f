"use client";

import { useState } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { 
  CreditCard, 
  Plus, 
  Shield, 
  Lock, 
  Globe,
  Building,
  User,
  MapPin,
  Phone,
  Mail,
  Check,
  ShoppingBag
} from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

interface CardRequestForm {
  type: "virtual" | "physical";
  cardType: "debit" | "credit";
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  deliveryMethod: "standard" | "express";
  cardDesign: "classic" | "premium";
  cardFeatures: {
    internationalTransactions: boolean;
    onlinePurchases: boolean;
    atmWithdrawals: boolean;
    contactlessPayments: boolean;
  };
}

interface Card {
  id: string
  type: 'debit' | 'credit'
  number: string
  expiry: string
  cvv: string
  name: string
  brand: string
  balance: number
  currency: string
  status: 'active' | 'inactive' | 'blocked'
}

// Demo data - replace with actual data from your backend
const cards = [
  {
    id: 1,
    type: "Virtual",
    name: "Virtual Debit Card",
    number: "**** **** **** 1234",
    expiryDate: "12/25",
    balance: 1250.00,
    currency: "USD",
    features: {
      international: true,
      online: true,
      contactless: false,
      frozen: false
    }
  },
  {
    id: 2,
    type: "Physical",
    name: "Premium Debit Card",
    number: "**** **** **** 5678",
    expiryDate: "06/26",
    balance: 3500.00,
    currency: "USD",
    features: {
      international: true,
      online: true,
      contactless: true,
      frozen: false
    }
  },
  {
    id: 3,
    type: "Physical",
    name: "Local Debit Card",
    number: "**** **** **** 9012",
    expiryDate: "09/24",
    balance: 15000.00,
    currency: "ZWL",
    features: {
      international: false,
      online: true,
      contactless: true,
      frozen: true
    }
  }
]

export default function CardsPage() {
  const [showNewCard, setShowNewCard] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const [formData, setFormData] = useState<CardRequestForm>({
    type: "virtual",
    cardType: "debit",
    name: "",
    email: "",
    phone: "",
    address: {
      street: "",
      city: "",
      state: "",
      country: "",
      postalCode: "",
    },
    deliveryMethod: "standard",
    cardDesign: "classic",
    cardFeatures: {
      internationalTransactions: true,
      onlinePurchases: true,
      atmWithdrawals: true,
      contactlessPayments: true,
    },
  });

  const [selectedCard, setSelectedCard] = useState<Card | null>(null)

  const handleInputChange = (
    field: keyof CardRequestForm,
    value: CardRequestForm[keyof CardRequestForm]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddressChange = (field: keyof CardRequestForm["address"], value: string) => {
    setFormData(prev => ({
      ...prev,
      address: { ...prev.address, [field]: value }
    }));
  };

  const handleFeatureToggle = (feature: keyof CardRequestForm["cardFeatures"]) => {
    setFormData(prev => ({
      ...prev,
      cardFeatures: {
        ...prev.cardFeatures,
        [feature]: !prev.cardFeatures[feature]
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Clear form
      setFormData({
        type: "virtual",
        cardType: "debit",
        name: "",
        email: "",
        phone: "",
        address: {
          street: "",
          city: "",
          state: "",
          country: "",
          postalCode: "",
        },
        deliveryMethod: "standard",
        cardDesign: "classic",
        cardFeatures: {
          internationalTransactions: true,
          onlinePurchases: true,
          atmWithdrawals: true,
          contactlessPayments: true,
        },
      });

      toast({
        title: "Success!",
        description: "Card request submitted successfully.",
      });

      setShowNewCard(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit card request. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <DashboardShell>
      <div className="space-y-4 sm:space-y-6 px-4 sm:px-0">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Cards</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage your virtual and physical cards
            </p>
          </div>
          <Button onClick={() => setShowNewCard(true)} className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Request New Card
          </Button>
        </div>

        {showNewCard && (
          <Card>
            <CardHeader className="space-y-2 sm:space-y-3">
              <CardTitle className="text-lg sm:text-xl">Request New Card</CardTitle>
              <CardDescription className="text-sm sm:text-base">
                Choose between a virtual or physical card
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 sm:space-y-6">
                <div className="space-y-2">
                  <Label className="text-sm sm:text-base">Card Type</Label>
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                    <Button
                      variant={formData.type === "virtual" ? "default" : "outline"}
                      className="flex-1 gap-2"
                      onClick={() => handleInputChange("type", "virtual")}
                    >
                      <CreditCard className="h-4 w-4" />
                      Virtual Card
                    </Button>
                    <Button
                      variant={formData.type === "physical" ? "default" : "outline"}
                      className="flex-1 gap-2"
                      onClick={() => handleInputChange("type", "physical")}
                    >
                      <CreditCard className="h-4 w-4" />
                      Physical Card
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm sm:text-base">Card Category</Label>
                  <RadioGroup
                    value={formData.cardType}
                    onValueChange={(value: "debit" | "credit") => handleInputChange("cardType", value)}
                    className="flex flex-col sm:flex-row gap-2 sm:gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="debit" id="debit" />
                      <Label htmlFor="debit" className="text-sm sm:text-base">Debit Card</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="credit" id="credit" />
                      <Label htmlFor="credit" className="text-sm sm:text-base">Credit Card</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm sm:text-base">Full Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange("name", e.target.value)}
                      placeholder="Enter your full name"
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm sm:text-base">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      placeholder="Enter your email"
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-sm sm:text-base">Phone Number</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    placeholder="Enter your phone number"
                    className="w-full"
                  />
                </div>

                {formData.type === "physical" && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-sm sm:text-base">Delivery Method</Label>
                      <Select
                        value={formData.deliveryMethod}
                        onValueChange={(value: "standard" | "express") => handleInputChange("deliveryMethod", value)}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select delivery method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="standard">Standard Delivery (5-7 days)</SelectItem>
                          <SelectItem value="express">Express Delivery (2-3 days)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm sm:text-base">Card Design</Label>
                      <RadioGroup
                        value={formData.cardDesign}
                        onValueChange={(value: "classic" | "premium") => handleInputChange("cardDesign", value)}
                        className="flex flex-col sm:flex-row gap-2 sm:gap-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="classic" id="classic" />
                          <Label htmlFor="classic" className="text-sm sm:text-base">Classic Design</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="premium" id="premium" />
                          <Label htmlFor="premium" className="text-sm sm:text-base">Premium Design</Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="space-y-4">
                      <Label className="text-sm sm:text-base">Delivery Address</Label>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="street" className="text-sm sm:text-base">Street Address</Label>
                          <Input
                            id="street"
                            value={formData.address.street}
                            onChange={(e) => handleAddressChange("street", e.target.value)}
                            placeholder="Enter street address"
                            className="w-full"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="city" className="text-sm sm:text-base">City</Label>
                          <Input
                            id="city"
                            value={formData.address.city}
                            onChange={(e) => handleAddressChange("city", e.target.value)}
                            placeholder="Enter city"
                            className="w-full"
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="state" className="text-sm sm:text-base">State/Province</Label>
                          <Input
                            id="state"
                            value={formData.address.state}
                            onChange={(e) => handleAddressChange("state", e.target.value)}
                            placeholder="Enter state"
                            className="w-full"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="country" className="text-sm sm:text-base">Country</Label>
                          <Input
                            id="country"
                            value={formData.address.country}
                            onChange={(e) => handleAddressChange("country", e.target.value)}
                            placeholder="Enter country"
                            className="w-full"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="postalCode" className="text-sm sm:text-base">Postal Code</Label>
                          <Input
                            id="postalCode"
                            value={formData.address.postalCode}
                            onChange={(e) => handleAddressChange("postalCode", e.target.value)}
                            placeholder="Enter postal code"
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}

                <div className="space-y-4">
                  <Label className="text-sm sm:text-base">Card Features</Label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="internationalTransactions"
                        checked={formData.cardFeatures.internationalTransactions}
                        onCheckedChange={() => handleFeatureToggle("internationalTransactions")}
                      />
                      <Label htmlFor="internationalTransactions" className="flex items-center gap-2 text-sm sm:text-base">
                        <Globe className="h-4 w-4" />
                        International Transactions
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="onlinePurchases"
                        checked={formData.cardFeatures.onlinePurchases}
                        onCheckedChange={() => handleFeatureToggle("onlinePurchases")}
                      />
                      <Label htmlFor="onlinePurchases" className="flex items-center gap-2 text-sm sm:text-base">
                        <ShoppingBag className="h-4 w-4" />
                        Online Purchases
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="atmWithdrawals"
                        checked={formData.cardFeatures.atmWithdrawals}
                        onCheckedChange={() => handleFeatureToggle("atmWithdrawals")}
                      />
                      <Label htmlFor="atmWithdrawals" className="flex items-center gap-2 text-sm sm:text-base">
                        <Building className="h-4 w-4" />
                        ATM Withdrawals
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="contactlessPayments"
                        checked={formData.cardFeatures.contactlessPayments}
                        onCheckedChange={() => handleFeatureToggle("contactlessPayments")}
                      />
                      <Label htmlFor="contactlessPayments" className="flex items-center gap-2 text-sm sm:text-base">
                        <CreditCard className="h-4 w-4" />
                        Contactless Payments
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row justify-between gap-2 sm:gap-0">
              <Button variant="outline" onClick={() => setShowNewCard(false)} className="w-full sm:w-auto">
                Cancel
              </Button>
              <Button className="w-full sm:w-auto gap-2">
                <Check className="h-4 w-4" />
                Request Card
              </Button>
            </CardFooter>
          </Card>
        )}

        <div className="grid gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-3">
          {cards.map((card) => (
            <Card key={card.id} className={cn(
              "relative overflow-hidden",
              card.features.frozen && "opacity-75"
            )}>
              {card.features.frozen && (
                <div className="absolute inset-0 bg-background/50 backdrop-blur-sm z-10 flex items-center justify-center">
                  <p className="text-muted-foreground font-semibold">Card Frozen</p>
                </div>
              )}
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    {card.name}
                  </span>
                  <span className="text-xs sm:text-sm font-normal text-muted-foreground">
                    {card.type}
                  </span>
                </CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  {card.number} • Expires {card.expiryDate}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-muted-foreground">Available Balance</p>
                    <p className="text-lg sm:text-2xl font-bold">
                      {card.currency} {card.balance.toLocaleString()}
                    </p>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <span className="text-xs sm:text-sm">
                        {card.features.international ? "International" : "Local Only"}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                      <span className="text-xs sm:text-sm">
                        {card.features.online ? "Online Enabled" : "No Online"}
                      </span>
                    </div>
                    {card.type === "Physical" && (
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        <span className="text-xs sm:text-sm">
                          {card.features.contactless ? "Contactless" : "No Contactless"}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col sm:flex-row justify-between gap-2 sm:gap-0">
                <Button variant="outline" size="sm" className="w-full sm:w-auto">
                  View Details
                </Button>
                <div className="flex items-center gap-2 w-full sm:w-auto">
                  <Switch
                    id={`freeze-${card.id}`}
                    checked={card.features.frozen}
                    onCheckedChange={() => {}}
                  />
                  <span className="text-xs sm:text-sm">Freeze Card</span>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </DashboardShell>
  );
}
