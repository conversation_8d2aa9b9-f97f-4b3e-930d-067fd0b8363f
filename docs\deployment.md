# Deployment Guide

This guide covers the deployment process for the ZB UniWallet platform.

## Building for Production

1. Create a production build:
```bash
npm run build
```

2. The build process will generate static files in the `out` directory.

## Server Requirements

- Apache 2.4+ or Nginx 1.16+
- PHP 7.4+ (if using PHP features)
- SSL Certificate (Required for secure transactions)
- Node.js 18.0+ (for build process only)

## Apache Deployment

1. Upload the contents of the `out` directory to your web server's root directory.

2. Ensure the `.htaccess` file is present and contains:
```apache
# Enable rewrite engine
RewriteEngine On

# If the request is not for a file that exists
RewriteCond %{REQUEST_FILENAME} !-f
# If the request is not for a directory that exists
RewriteCond %{REQUEST_FILENAME} !-d
# Rewrite all requests to the index.html file
RewriteRule ^(.*)$ /index.html [L]
```

3. Configure proper file permissions:
```bash
chmod 644 .htaccess
chmod 644 *.html
chmod 644 *.js
chmod 644 *.css
chmod 755 images/
```

## Nginx Deployment

1. Upload the contents of the `out` directory to your web server.

2. Configure your Nginx server block:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/out;
    index index.html;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    root /path/to/out;
    index index.html;

    # SSL Configuration
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()";

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location /static {
        expires 1y;
        add_header Cache-Control "public, no-transform";
    }

    # Gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

## Environment Configuration

1. Set up environment variables:
```env
NEXT_PUBLIC_API_URL=https://api.your-domain.com
NEXT_PUBLIC_ENVIRONMENT=production
```

2. Configure API endpoints in the application:
```typescript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.default-domain.com';
```

## SSL Configuration

1. Install SSL certificate:
```bash
certbot --nginx -d your-domain.com
```

2. Configure automatic renewal:
```bash
certbot renew --dry-run
```

## Post-Deployment Checklist

- [ ] Verify all routes work correctly
- [ ] Test all API endpoints
- [ ] Check SSL certificate installation
- [ ] Verify security headers
- [ ] Test mobile responsiveness
- [ ] Monitor error logs
- [ ] Set up monitoring tools
- [ ] Configure backup system

## Troubleshooting

### Common Issues

1. **Routing Issues**
   - Ensure .htaccess file is present and properly configured
   - Check server rewrite module is enabled
   - Verify file permissions

2. **API Connection Issues**
   - Verify API endpoint configuration
   - Check CORS settings
   - Validate SSL certificates

3. **Performance Issues**
   - Enable server-side caching
   - Optimize static assets
   - Configure CDN if needed

### Monitoring

1. Set up monitoring tools:
   - Server monitoring (CPU, Memory, Disk)
   - Application monitoring (Error tracking)
   - Performance monitoring (Page load times)
   - Security monitoring (Failed login attempts)

2. Configure alerts for:
   - Server downtime
   - High error rates
   - Performance degradation
   - Security incidents

## Backup Strategy

1. Regular backups of:
   - Application files
   - Configuration files
   - SSL certificates
   - Environment variables

2. Backup schedule:
   - Daily incremental backups
   - Weekly full backups
   - Monthly archives

## Support Contacts

For deployment support:
- Technical Team: <EMAIL>
- Emergency Support: +263-242-758280
- Documentation: https://docs.zb.co.zw 