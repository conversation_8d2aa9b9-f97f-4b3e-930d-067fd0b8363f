# 2. Coding Standards and Guidelines

## 📋 **Coding Standards Overview**

This document establishes comprehensive coding standards and guidelines for the UniversalWallet platform, ensuring code quality, maintainability, security, and consistency across all development teams and technologies.

## 🎯 **General Coding Principles**

### **Core Principles**
- **Clean Code**: Write code that is easy to read, understand, and maintain
- **SOLID Principles**: Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion
- **DRY (Don't Repeat Yourself)**: Avoid code duplication
- **KISS (Keep It Simple, Stupid)**: Prefer simple solutions over complex ones
- **Security First**: Always consider security implications in code design

### **Code Quality Standards**
- **Test Coverage**: Minimum 80% code coverage for all modules
- **Documentation**: Comprehensive inline documentation and README files
- **Performance**: Optimize for performance without sacrificing readability
- **Error Handling**: Comprehensive error handling and logging
- **Code Reviews**: All code must be reviewed before merging

---

## ☕ **Java/Spring Boot Standards**

### **Naming Conventions**

#### **Classes and Interfaces**
```java
// Classes: PascalCase
public class UserService {
    // Implementation
}

// Interfaces: PascalCase with descriptive names
public interface PaymentProcessor {
    // Interface methods
}

// Abstract classes: PascalCase with "Abstract" prefix
public abstract class AbstractTransactionHandler {
    // Abstract implementation
}

// Enums: PascalCase
public enum TransactionStatus {
    PENDING, PROCESSING, COMPLETED, FAILED
}
```

#### **Methods and Variables**
```java
// Methods: camelCase with descriptive verbs
public TransactionResult processPayment(PaymentRequest request) {
    // Local variables: camelCase
    String transactionReference = generateReference();
    BigDecimal totalAmount = calculateTotal(request);
    
    // Constants: UPPER_SNAKE_CASE
    private static final String DEFAULT_CURRENCY = "ZWG";
    private static final int MAX_RETRY_ATTEMPTS = 3;
    
    return new TransactionResult(transactionReference, totalAmount);
}
```

### **Package Structure**
```
com.universalwallet
├── config/              # Configuration classes
├── controller/          # REST controllers
│   ├── api/v1/         # API version 1 controllers
│   └── admin/          # Admin controllers
├── service/            # Business logic services
│   ├── impl/           # Service implementations
│   └── external/       # External service integrations
├── repository/         # Data access layer
├── entity/             # JPA entities
├── dto/                # Data Transfer Objects
│   ├── request/        # Request DTOs
│   └── response/       # Response DTOs
├── security/           # Security configurations
├── exception/          # Custom exceptions
├── util/               # Utility classes
└── integration/        # External integrations
```

### **Spring Boot Best Practices**

#### **Controller Layer**
```java
@RestController
@RequestMapping("/api/v1/transactions")
@Validated
@Slf4j
public class TransactionController {
    
    private final TransactionService transactionService;
    
    public TransactionController(TransactionService transactionService) {
        this.transactionService = transactionService;
    }
    
    @PostMapping("/transfer")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<TransactionResponse> createTransfer(
            @Valid @RequestBody TransferRequest request,
            Authentication authentication) {
        
        log.info("Creating transfer for user: {}", authentication.getName());
        
        try {
            TransactionResponse response = transactionService.processTransfer(request);
            return ResponseEntity.ok(response);
        } catch (InsufficientBalanceException e) {
            log.warn("Insufficient balance for transfer: {}", e.getMessage());
            throw new BadRequestException("Insufficient balance");
        }
    }
}
```

#### **Service Layer**
```java
@Service
@Transactional
@Slf4j
public class TransactionServiceImpl implements TransactionService {
    
    private final TransactionRepository transactionRepository;
    private final AccountService accountService;
    private final NotificationService notificationService;
    
    @Override
    public TransactionResponse processTransfer(TransferRequest request) {
        // Validate request
        validateTransferRequest(request);
        
        // Create transaction entity
        Transaction transaction = createTransaction(request);
        
        // Process transfer
        TransactionResult result = executeTransfer(transaction);
        
        // Send notifications
        notificationService.sendTransferNotification(result);
        
        return mapToResponse(result);
    }
    
    private void validateTransferRequest(TransferRequest request) {
        if (request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidAmountException("Amount must be positive");
        }
        // Additional validations
    }
}
```

### **Error Handling**
```java
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidation(ValidationException e) {
        log.warn("Validation error: {}", e.getMessage());
        
        ErrorResponse error = ErrorResponse.builder()
            .code("VALIDATION_ERROR")
            .message(e.getMessage())
            .timestamp(Instant.now())
            .build();
            
        return ResponseEntity.badRequest().body(error);
    }
    
    @ExceptionHandler(InsufficientBalanceException.class)
    public ResponseEntity<ErrorResponse> handleInsufficientBalance(InsufficientBalanceException e) {
        log.warn("Insufficient balance: {}", e.getMessage());
        
        ErrorResponse error = ErrorResponse.builder()
            .code("INSUFFICIENT_BALANCE")
            .message("Insufficient balance for this transaction")
            .details(Map.of("available_balance", e.getAvailableBalance()))
            .timestamp(Instant.now())
            .build();
            
        return ResponseEntity.badRequest().body(error);
    }
}
```

---

## 🌐 **TypeScript/React Standards**

### **Naming Conventions**

#### **Files and Directories**
```
src/
├── components/
│   ├── common/
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx
│   │   │   ├── Button.styles.ts
│   │   │   └── index.ts
│   └── features/
│       └── Transaction/
│           ├── TransactionForm.tsx
│           └── TransactionList.tsx
├── hooks/
│   ├── useAuth.ts
│   └── useTransaction.ts
├── services/
│   ├── api/
│   │   ├── authApi.ts
│   │   └── transactionApi.ts
│   └── utils/
├── types/
│   ├── auth.types.ts
│   └── transaction.types.ts
└── utils/
    ├── formatters.ts
    └── validators.ts
```

#### **Component Structure**
```typescript
// TransactionForm.tsx
import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, TextField, Alert } from '@mui/material';
import { useTransaction } from '../../hooks/useTransaction';
import { TransferRequest, TransactionResponse } from '../../types/transaction.types';
import { formatCurrency, validateAmount } from '../../utils/formatters';

interface TransactionFormProps {
  onSuccess: (transaction: TransactionResponse) => void;
  onError: (error: string) => void;
  initialData?: Partial<TransferRequest>;
}

export const TransactionForm: React.FC<TransactionFormProps> = ({
  onSuccess,
  onError,
  initialData
}) => {
  const { t } = useTranslation();
  const { createTransfer, isLoading } = useTransaction();
  
  const [formData, setFormData] = useState<TransferRequest>({
    recipientPhone: initialData?.recipientPhone || '',
    amount: initialData?.amount || 0,
    description: initialData?.description || '',
    sourceAccountId: initialData?.sourceAccountId || ''
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    
    // Validate form
    const validationErrors = validateForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    try {
      const result = await createTransfer(formData);
      onSuccess(result);
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [formData, createTransfer, onSuccess, onError]);
  
  const validateForm = (data: TransferRequest): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    if (!data.recipientPhone) {
      errors.recipientPhone = t('validation.required');
    }
    
    if (!validateAmount(data.amount)) {
      errors.amount = t('validation.invalidAmount');
    }
    
    return errors;
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <TextField
        label={t('transaction.recipientPhone')}
        value={formData.recipientPhone}
        onChange={(e) => setFormData(prev => ({ ...prev, recipientPhone: e.target.value }))}
        error={!!errors.recipientPhone}
        helperText={errors.recipientPhone}
        fullWidth
        margin="normal"
      />
      
      <TextField
        label={t('transaction.amount')}
        type="number"
        value={formData.amount}
        onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) }))}
        error={!!errors.amount}
        helperText={errors.amount}
        fullWidth
        margin="normal"
      />
      
      <Button
        type="submit"
        variant="contained"
        disabled={isLoading}
        fullWidth
      >
        {isLoading ? t('common.processing') : t('transaction.send')}
      </Button>
    </form>
  );
};

export default TransactionForm;
```

### **TypeScript Best Practices**

#### **Type Definitions**
```typescript
// transaction.types.ts
export interface User {
  readonly id: string;
  readonly phoneNumber: string;
  readonly email?: string;
  readonly firstName: string;
  readonly lastName: string;
  readonly kycLevel: KycLevel;
  readonly status: UserStatus;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export interface TransferRequest {
  recipientPhone: string;
  amount: number;
  currency: string;
  sourceAccountId: string;
  description?: string;
}

export interface TransactionResponse {
  readonly id: string;
  readonly referenceNumber: string;
  readonly status: TransactionStatus;
  readonly amount: number;
  readonly fee: number;
  readonly totalAmount: number;
  readonly recipient: {
    readonly phone: string;
    readonly name: string;
  };
  readonly createdAt: Date;
  readonly completedAt?: Date;
}

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum KycLevel {
  BASIC = 'basic',
  ENHANCED = 'enhanced'
}

export enum UserStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  PENDING = 'pending'
}
```

#### **Custom Hooks**
```typescript
// useTransaction.ts
import { useState, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { transactionApi } from '../services/api/transactionApi';
import { TransferRequest, TransactionResponse } from '../types/transaction.types';

export const useTransaction = () => {
  const queryClient = useQueryClient();
  
  const createTransferMutation = useMutation({
    mutationFn: (request: TransferRequest) => transactionApi.createTransfer(request),
    onSuccess: () => {
      // Invalidate and refetch transaction list
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
    }
  });
  
  const { data: transactions, isLoading: isLoadingTransactions } = useQuery({
    queryKey: ['transactions'],
    queryFn: () => transactionApi.getTransactions(),
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
  
  const createTransfer = useCallback(async (request: TransferRequest): Promise<TransactionResponse> => {
    return createTransferMutation.mutateAsync(request);
  }, [createTransferMutation]);
  
  return {
    transactions,
    isLoadingTransactions,
    createTransfer,
    isCreatingTransfer: createTransferMutation.isPending
  };
};
```

---

## 🔐 **Security Coding Standards**

### **Input Validation**
```java
// Java validation
@Valid
public class TransferRequest {
    @NotBlank(message = "Recipient phone is required")
    @Pattern(regexp = "^\\+263[0-9]{9}$", message = "Invalid phone number format")
    private String recipientPhone;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be positive")
    @DecimalMax(value = "50000.00", message = "Amount exceeds maximum limit")
    private BigDecimal amount;
    
    @NotBlank(message = "Source account is required")
    @UUID(message = "Invalid account ID format")
    private String sourceAccountId;
}
```

```typescript
// TypeScript validation
import * as yup from 'yup';

export const transferRequestSchema = yup.object({
  recipientPhone: yup
    .string()
    .required('Recipient phone is required')
    .matches(/^\+263[0-9]{9}$/, 'Invalid phone number format'),
  
  amount: yup
    .number()
    .required('Amount is required')
    .min(0.01, 'Amount must be positive')
    .max(50000, 'Amount exceeds maximum limit'),
  
  sourceAccountId: yup
    .string()
    .required('Source account is required')
    .uuid('Invalid account ID format')
});
```

### **Sensitive Data Handling**
```java
// Never log sensitive data
@Slf4j
public class PaymentService {
    
    public void processPayment(PaymentRequest request) {
        // Good: Log non-sensitive data
        log.info("Processing payment for user: {}, amount: {}", 
                request.getUserId(), request.getAmount());
        
        // Bad: Never log sensitive data
        // log.info("Processing payment with PIN: {}", request.getPin());
        
        // Use data masking for logging
        log.debug("Processing payment for phone: {}", 
                maskPhoneNumber(request.getRecipientPhone()));
    }
    
    private String maskPhoneNumber(String phone) {
        if (phone.length() < 4) return "****";
        return phone.substring(0, 4) + "****" + phone.substring(phone.length() - 2);
    }
}
```

---

## 📝 **Documentation Standards**

### **Code Documentation**
```java
/**
 * Processes interoperable money transfers between different financial providers.
 * 
 * This service handles the complex orchestration of transfers between UniversalWallet
 * and external providers like EcoCash, OneMoney, and banks. It implements the Saga
 * pattern to ensure transaction consistency across multiple systems.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Service
@Transactional
public class InteroperableTransferService {
    
    /**
     * Processes a transfer between different financial providers.
     * 
     * @param request The transfer request containing recipient, amount, and source details
     * @return TransactionResponse containing transaction details and status
     * @throws InsufficientBalanceException if source account has insufficient funds
     * @throws InvalidRecipientException if recipient details are invalid
     * @throws ExternalServiceException if external provider is unavailable
     */
    public TransactionResponse processInteroperableTransfer(TransferRequest request) {
        // Implementation
    }
}
```

### **API Documentation**
```java
@RestController
@RequestMapping("/api/v1/transactions")
@Tag(name = "Transactions", description = "Transaction management operations")
public class TransactionController {
    
    @PostMapping("/transfer")
    @Operation(
        summary = "Create money transfer",
        description = "Creates a new money transfer between accounts or to external providers"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Transfer created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "409", description = "Insufficient balance")
    })
    public ResponseEntity<TransactionResponse> createTransfer(
            @Parameter(description = "Transfer request details")
            @Valid @RequestBody TransferRequest request) {
        // Implementation
    }
}
```

**These comprehensive coding standards ensure high-quality, secure, and maintainable code across the entire UniversalWallet platform.** 📋
