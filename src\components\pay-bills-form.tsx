import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { WalletSelectorTabs } from './wallet-selector-tabs'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'

const BILL_TYPES = [
  { value: 'electricity', label: 'Electricity' },
  { value: 'water', label: 'Water' },
  { value: 'internet', label: 'Internet' },
  { value: 'tv', label: 'TV Subscription' },
  { value: 'other', label: 'Other' },
]

export function PayBillsForm() {
  const router = useRouter()
  const [selectedWalletId, setSelectedWalletId] = useState<string>()
  const [billType, setBillType] = useState('')
  const [accountNumber, setAccountNumber] = useState('')
  const [amount, setAmount] = useState('')

  const handleAddWallet = () => {
    router.push('/wallets')
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>Pay Bills</CardTitle>
          <CardDescription>Choose a wallet to pay your bills from</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <WalletSelectorTabs
            selectedWalletId={selectedWalletId}
            onWalletSelect={setSelectedWalletId}
            onAddWallet={handleAddWallet}
          />

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bill-type">Bill Type</Label>
              <Select value={billType} onValueChange={setBillType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select bill type" />
                </SelectTrigger>
                <SelectContent>
                  {BILL_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="account">Account Number</Label>
              <Input
                id="account"
                placeholder="Enter account or meter number"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Enter amount to pay"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
              />
            </div>

            <Button
              className="w-full"
              disabled={!selectedWalletId || !billType || !accountNumber || !amount}
            >
              Pay Bill
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
} 