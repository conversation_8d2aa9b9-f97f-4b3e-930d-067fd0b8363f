# 2. Database Schema and Design

## 🗄️ **Database Design Overview**

This document outlines the comprehensive database schema for the UniversalWallet platform, designed to support all user types, transaction processing, compliance requirements, and enterprise-level operations with full audit trails and data integrity.

## 🏗️ **Database Architecture**

### **Primary Database: PostgreSQL 15+**
- **ACID Compliance**: Full transaction integrity
- **JSON Support**: Flexible data structures for metadata
- **Advanced Indexing**: Performance optimization
- **Partitioning**: Large table management
- **Replication**: High availability and read scaling

### **Supporting Databases**
- **Redis**: Session management, caching, real-time data
- **Elasticsearch**: Search, analytics, and reporting
- **AWS S3**: Document storage and file management

---

## 👤 **User Management Schema**

### **users Table**
```sql
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    country_code VARCHAR(5) NOT NULL DEFAULT '+263',
    email VARCHAR(255) UNIQUE,
    user_type ENUM('personal', 'business', 'agent', 'admin') NOT NULL,
    status ENUM('active', 'suspended', 'pending', 'blocked') DEFAULT 'pending',
    kyc_level ENUM('basic', 'enhanced', 'business', 'agent') DEFAULT 'basic',
    kyc_status ENUM('pending', 'approved', 'rejected', 'expired') DEFAULT 'pending',
    pin_hash VARCHAR(255) NOT NULL,
    biometric_enabled BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    metadata JSONB
);
```

### **user_profiles Table**
```sql
CREATE TABLE user_profiles (
    profile_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    national_id VARCHAR(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(20),
    occupation VARCHAR(100),
    employer VARCHAR(255),
    monthly_income DECIMAL(15,2),
    profile_picture_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **kyc_documents Table**
```sql
CREATE TABLE kyc_documents (
    document_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    document_type ENUM('national_id', 'passport', 'drivers_license', 'utility_bill', 'bank_statement', 'business_registration', 'tax_certificate') NOT NULL,
    document_url VARCHAR(500) NOT NULL,
    document_number VARCHAR(100),
    issue_date DATE,
    expiry_date DATE,
    verification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    verified_by UUID REFERENCES users(user_id),
    verified_at TIMESTAMP,
    rejection_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🏢 **Business User Schema**

### **business_profiles Table**
```sql
CREATE TABLE business_profiles (
    business_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    business_name VARCHAR(255) NOT NULL,
    business_type ENUM('sole_proprietorship', 'partnership', 'private_company', 'public_company', 'ngo', 'government') NOT NULL,
    registration_number VARCHAR(100) UNIQUE,
    tax_number VARCHAR(100),
    industry VARCHAR(100),
    business_description TEXT,
    website_url VARCHAR(255),
    business_phone VARCHAR(20),
    business_email VARCHAR(255),
    business_address_line1 VARCHAR(255),
    business_address_line2 VARCHAR(255),
    business_city VARCHAR(100),
    business_province VARCHAR(100),
    business_postal_code VARCHAR(20),
    annual_revenue DECIMAL(15,2),
    employee_count INTEGER,
    business_logo_url VARCHAR(500),
    verification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **business_users Table**
```sql
CREATE TABLE business_users (
    business_user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_id UUID REFERENCES business_profiles(business_id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    role ENUM('owner', 'admin', 'finance_manager', 'payer', 'viewer') NOT NULL,
    permissions JSONB NOT NULL,
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    invited_by UUID REFERENCES users(user_id),
    invited_at TIMESTAMP,
    joined_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(business_id, user_id)
);
```

---

## 🤝 **Agent Network Schema**

### **agent_profiles Table**
```sql
CREATE TABLE agent_profiles (
    agent_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    agent_code VARCHAR(20) UNIQUE NOT NULL,
    agent_type ENUM('individual', 'business', 'franchise') NOT NULL,
    business_name VARCHAR(255),
    business_license VARCHAR(100),
    location_name VARCHAR(255) NOT NULL,
    location_address TEXT NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    operating_hours JSONB,
    services_offered JSONB NOT NULL,
    commission_rate DECIMAL(5,4) DEFAULT 0.02,
    float_limit DECIMAL(15,2) DEFAULT 50000.00,
    current_float DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'inactive', 'suspended', 'training') DEFAULT 'training',
    certification_date DATE,
    certification_expiry DATE,
    performance_rating DECIMAL(3,2) DEFAULT 0.00,
    total_transactions INTEGER DEFAULT 0,
    total_commission DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **agent_float_transactions Table**
```sql
CREATE TABLE agent_float_transactions (
    float_transaction_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES agent_profiles(agent_id) ON DELETE CASCADE,
    transaction_type ENUM('float_in', 'float_out', 'commission_settlement') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    previous_balance DECIMAL(15,2) NOT NULL,
    new_balance DECIMAL(15,2) NOT NULL,
    reference_number VARCHAR(100),
    description TEXT,
    processed_by UUID REFERENCES users(user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 💰 **Account Management Schema**

### **linked_accounts Table**
```sql
CREATE TABLE linked_accounts (
    account_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    provider_name ENUM('ecocash', 'onemoney', 'innbucks', 'cabs', 'stanbic', 'barclays', 'fbc', 'steward', 'universalwallet') NOT NULL,
    account_type ENUM('mobile_money', 'bank_account', 'savings', 'current', 'wallet') NOT NULL,
    account_number VARCHAR(100),
    account_name VARCHAR(255),
    currency VARCHAR(3) DEFAULT 'ZWG',
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_balance DECIMAL(15,2) DEFAULT 0.00,
    last_balance_update TIMESTAMP,
    connection_status ENUM('connected', 'disconnected', 'error', 'pending') DEFAULT 'pending',
    connection_metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, provider_name, account_number)
);
```

### **account_balances Table**
```sql
CREATE TABLE account_balances (
    balance_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES linked_accounts(account_id) ON DELETE CASCADE,
    balance DECIMAL(15,2) NOT NULL,
    available_balance DECIMAL(15,2) NOT NULL,
    pending_balance DECIMAL(15,2) DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL,
    balance_timestamp TIMESTAMP NOT NULL,
    sync_method ENUM('api', 'webhook', 'manual', 'scheduled') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 💸 **Transaction Management Schema**

### **transactions Table**
```sql
CREATE TABLE transactions (
    transaction_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reference_number VARCHAR(100) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(user_id),
    transaction_type ENUM('p2p_transfer', 'bill_payment', 'cash_in', 'cash_out', 'wallet_to_bank', 'bank_to_wallet', 'interoperable_transfer', 'bulk_payment', 'agent_commission', 'refund') NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed') DEFAULT 'pending',
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZWG',
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) NOT NULL,
    source_account_id UUID REFERENCES linked_accounts(account_id),
    destination_account_id UUID REFERENCES linked_accounts(account_id),
    recipient_phone VARCHAR(20),
    recipient_name VARCHAR(255),
    description TEXT,
    metadata JSONB,
    initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    failed_at TIMESTAMP,
    failure_reason TEXT,
    external_reference VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **transaction_legs Table**
```sql
CREATE TABLE transaction_legs (
    leg_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID REFERENCES transactions(transaction_id) ON DELETE CASCADE,
    leg_sequence INTEGER NOT NULL,
    leg_type ENUM('debit', 'credit') NOT NULL,
    account_id UUID REFERENCES linked_accounts(account_id),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'reversed') DEFAULT 'pending',
    external_reference VARCHAR(255),
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(transaction_id, leg_sequence)
);
```

---

## 🧾 **Payment and Billing Schema**

### **bill_payments Table**
```sql
CREATE TABLE bill_payments (
    payment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID REFERENCES transactions(transaction_id),
    user_id UUID REFERENCES users(user_id),
    biller_code VARCHAR(50) NOT NULL,
    biller_name VARCHAR(255) NOT NULL,
    account_number VARCHAR(100) NOT NULL,
    customer_name VARCHAR(255),
    bill_amount DECIMAL(15,2) NOT NULL,
    payment_amount DECIMAL(15,2) NOT NULL,
    payment_method ENUM('single_source', 'combined_balance') NOT NULL,
    payment_sources JSONB,
    bill_reference VARCHAR(255),
    due_date DATE,
    payment_status ENUM('pending', 'paid', 'failed', 'partial') DEFAULT 'pending',
    confirmation_code VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **invoices Table**
```sql
CREATE TABLE invoices (
    invoice_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_id UUID REFERENCES business_profiles(business_id),
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255),
    customer_phone VARCHAR(20),
    invoice_amount DECIMAL(15,2) NOT NULL,
    tax_amount DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZWG',
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    payment_link VARCHAR(500),
    qr_code_url VARCHAR(500),
    line_items JSONB NOT NULL,
    notes TEXT,
    terms_conditions TEXT,
    paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔐 **Security and Audit Schema**

### **audit_logs Table**
```sql
CREATE TABLE audit_logs (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low'
);
```

### **security_events Table**
```sql
CREATE TABLE security_events (
    event_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id),
    event_type ENUM('login_success', 'login_failure', 'password_change', 'account_locked', 'suspicious_activity', 'fraud_detected') NOT NULL,
    event_details JSONB NOT NULL,
    risk_score INTEGER DEFAULT 0,
    ip_address INET,
    device_fingerprint VARCHAR(255),
    location_data JSONB,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID REFERENCES users(user_id),
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🏛️ **Administrative Schema**

### **admin_users Table**
```sql
CREATE TABLE admin_users (
    admin_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    admin_level ENUM('super_admin', 'admin', 'compliance_officer', 'support_agent', 'analyst') NOT NULL,
    permissions JSONB NOT NULL,
    department VARCHAR(100),
    reporting_to UUID REFERENCES admin_users(admin_id),
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_activity TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **system_configurations Table**
```sql
CREATE TABLE system_configurations (
    config_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    config_type ENUM('system', 'business', 'security', 'integration') NOT NULL,
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    last_modified_by UUID REFERENCES users(user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 📊 **Analytics and Reporting Schema**

### **user_analytics Table**
```sql
CREATE TABLE user_analytics (
    analytics_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id),
    date DATE NOT NULL,
    login_count INTEGER DEFAULT 0,
    transaction_count INTEGER DEFAULT 0,
    transaction_volume DECIMAL(15,2) DEFAULT 0.00,
    feature_usage JSONB,
    session_duration INTEGER DEFAULT 0,
    device_type VARCHAR(50),
    app_version VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, date)
);
```

### **business_analytics Table**
```sql
CREATE TABLE business_analytics (
    analytics_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_id UUID REFERENCES business_profiles(business_id),
    date DATE NOT NULL,
    transaction_count INTEGER DEFAULT 0,
    transaction_volume DECIMAL(15,2) DEFAULT 0.00,
    bulk_payment_count INTEGER DEFAULT 0,
    invoice_count INTEGER DEFAULT 0,
    revenue DECIMAL(15,2) DEFAULT 0.00,
    customer_count INTEGER DEFAULT 0,
    api_calls INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(business_id, date)
);
```

---

## 🔍 **Indexes and Performance**

### **Critical Indexes**
```sql
-- User lookup indexes
CREATE INDEX idx_users_phone_number ON users(phone_number);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status_type ON users(status, user_type);

-- Transaction indexes
CREATE INDEX idx_transactions_user_id_date ON transactions(user_id, created_at DESC);
CREATE INDEX idx_transactions_reference ON transactions(reference_number);
CREATE INDEX idx_transactions_status_type ON transactions(status, transaction_type);

-- Account indexes
CREATE INDEX idx_linked_accounts_user_provider ON linked_accounts(user_id, provider_name);
CREATE INDEX idx_account_balances_account_timestamp ON account_balances(account_id, balance_timestamp DESC);

-- Audit and security indexes
CREATE INDEX idx_audit_logs_user_timestamp ON audit_logs(user_id, timestamp DESC);
CREATE INDEX idx_security_events_user_type ON security_events(user_id, event_type);
```

**This comprehensive database schema supports all UniversalWallet platform requirements with enterprise-level security, audit trails, and performance optimization.** 🗄️
