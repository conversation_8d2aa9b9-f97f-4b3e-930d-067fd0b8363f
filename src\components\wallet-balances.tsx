"use client";

import { WalletCard } from "@/components/wallet-card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { type Wallet } from "@/lib/data";

interface WalletBalancesProps {
  wallets: Wallet[];
}

export function WalletBalances({ wallets }: WalletBalancesProps) {
  const totalBalances = wallets.reduce(
    (total, wallet) => {
      return {
        ZWL: total.ZWL + wallet.balances.ZWL,
        USD: total.USD + wallet.balances.USD
      };
    },
    { ZWL: 0, USD: 0 }
  );

  // Find and separate ZB UniWallet if exists
  const zbUniWallet = wallets.find(wallet => wallet.id === "zbuniwallet-001");
  const otherWallets = wallets.filter(wallet => wallet.id !== "zbuniwallet-001");

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold">My Wallets</h2>
          <Tabs defaultValue="ZWL" className="mt-2 w-full">
            <TabsList className="grid w-36 grid-cols-2">
              <TabsTrigger value="ZWL">ZWL</TabsTrigger>
              <TabsTrigger value="USD">USD</TabsTrigger>
            </TabsList>
            <TabsContent value="ZWL" className="mt-2 p-0">
              <p className="text-sm text-muted-foreground">
                Total Balance: ZWL {totalBalances.ZWL.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </p>
            </TabsContent>
            <TabsContent value="USD" className="mt-2 p-0">
              <p className="text-sm text-muted-foreground">
                Total Balance: USD {totalBalances.USD.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </p>
            </TabsContent>
          </Tabs>
        </div>
        <Link href="/wallets">
          <Button variant="outline" className="gap-2">
            <span>View All Wallets</span>
          </Button>
        </Link>
      </div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Display ZB UniWallet first if it exists */}
        {zbUniWallet && (
          <WalletCard
            key={zbUniWallet.id}
            name={zbUniWallet.name}
            balances={zbUniWallet.balances}
            primaryCurrency={zbUniWallet.primaryCurrency}
            logo={zbUniWallet.logo}
            logoAlt={zbUniWallet.logoAlt}
            isConnected={zbUniWallet.isConnected}
            isPrimary={true}
          />
        )}

        {/* Display other wallets */}
        {otherWallets.map((wallet) => (
          <WalletCard
            key={wallet.id}
            name={wallet.name}
            balances={wallet.balances}
            primaryCurrency={wallet.primaryCurrency}
            logo={wallet.logo}
            logoAlt={wallet.logoAlt}
            isConnected={wallet.isConnected}
          />
        ))}
      </div>
    </div>
  );
}
