import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import "./globals.css"
import { Toaster } from "@/components/ui/toaster"

const rubik = Rubik({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-rubik",
  display: "swap"
})

export const metadata: Metadata = {
  title: "ZB Digital Wallet - Your Universal Digital Wallet",
  description: "A universal digital wallet system that integrates with existing money platforms in Zimbabwe.",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${rubik.variable} font-sans antialiased`}>
        {children}
        <Toaster />
      </body>
    </html>
  )
}
