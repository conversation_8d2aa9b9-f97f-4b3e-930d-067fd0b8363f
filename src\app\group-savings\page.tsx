"use client";

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardShell } from '@/components/dashboard-shell'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Users, 
  DollarSign, 
  Calendar, 
  TrendingUp, 
  Plus,
  Target,
  Clock,
  Award,
  ArrowUpIcon,
  ArrowDownIcon
} from 'lucide-react'

export default function GroupSavingsPage() {
  const router = useRouter()

  // Mock group savings data
  const myGroups = [
    {
      id: '1',
      name: 'Family Savings Circle',
      description: 'Monthly family savings for emergency fund',
      totalMembers: 8,
      myContribution: 2500,
      totalSaved: 18500,
      targetAmount: 50000,
      nextContribution: new Date('2024-02-01'),
      contributionAmount: 500,
      frequency: 'monthly',
      status: 'active',
      myRole: 'member',
      avatar: '/images/groups/family.png'
    },
    {
      id: '2',
      name: 'Office Investment Club',
      description: 'Workplace investment savings group',
      totalMembers: 12,
      myContribution: 4200,
      totalSaved: 45600,
      targetAmount: 100000,
      nextContribution: new Date('2024-01-25'),
      contributionAmount: 1000,
      frequency: 'bi-weekly',
      status: 'active',
      myRole: 'admin',
      avatar: '/images/groups/office.png'
    },
    {
      id: '3',
      name: 'Community Development Fund',
      description: 'Local community infrastructure projects',
      totalMembers: 25,
      myContribution: 1800,
      totalSaved: 32500,
      targetAmount: 75000,
      nextContribution: new Date('2024-02-15'),
      contributionAmount: 300,
      frequency: 'monthly',
      status: 'active',
      myRole: 'treasurer',
      avatar: '/images/groups/community.png'
    }
  ]

  const recentActivity = [
    {
      id: 1,
      type: 'contribution',
      groupName: 'Family Savings Circle',
      member: 'You',
      amount: 500,
      date: 'Today, 9:00 AM',
      status: 'completed'
    },
    {
      id: 2,
      type: 'contribution',
      groupName: 'Office Investment Club',
      member: 'Sarah Wilson',
      amount: 1000,
      date: 'Yesterday, 2:30 PM',
      status: 'completed'
    },
    {
      id: 3,
      type: 'payout',
      groupName: 'Community Development Fund',
      member: 'Mike Johnson',
      amount: 5000,
      date: 'Jan 20, 11:15 AM',
      status: 'completed'
    },
    {
      id: 4,
      type: 'contribution',
      groupName: 'Family Savings Circle',
      member: 'John Doe',
      amount: 500,
      date: 'Jan 19, 4:45 PM',
      status: 'completed'
    }
  ]

  const totalSaved = myGroups.reduce((sum, group) => sum + group.myContribution, 0)
  const totalGroupSavings = myGroups.reduce((sum, group) => sum + group.totalSaved, 0)

  return (
    <DashboardShell>
      <div className="container px-4 sm:px-6 space-y-6 sm:space-y-8 pb-20 md:pb-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Group Savings (Mukando)</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Collaborative savings with family, friends, and community
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={() => router.push('/group-savings/create')}>
              <Plus className="h-4 w-4 mr-2" />
              Create Group
            </Button>
            <Button variant="outline" onClick={() => router.push('/group-savings/join')}>
              Join Group
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">My Total Savings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {totalSaved.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Across all groups</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Groups</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{myGroups.length}</div>
              <p className="text-xs text-muted-foreground">Groups you're part of</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Group Total</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {totalGroupSavings.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Combined group savings</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Next Contribution</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Jan 25</div>
              <p className="text-xs text-muted-foreground">Office Investment Club</p>
            </CardContent>
          </Card>
        </div>

        {/* My Groups */}
        <div>
          <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4">My Savings Groups</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {myGroups.map((group) => (
              <Card key={group.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={group.avatar} alt={group.name} />
                        <AvatarFallback>{group.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">{group.name}</CardTitle>
                        <CardDescription>{group.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                        {group.status}
                      </Badge>
                      {group.myRole === 'admin' && (
                        <Badge variant="outline">
                          <Award className="h-3 w-3 mr-1" />
                          Admin
                        </Badge>
                      )}
                      {group.myRole === 'treasurer' && (
                        <Badge variant="outline">
                          <DollarSign className="h-3 w-3 mr-1" />
                          Treasurer
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Members</p>
                      <p className="font-semibold">{group.totalMembers}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">My Contribution</p>
                      <p className="font-semibold">USD {group.myContribution.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Next Payment</p>
                      <p className="font-semibold">USD {group.contributionAmount}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Frequency</p>
                      <p className="font-semibold capitalize">{group.frequency}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress to Target</span>
                      <span>{Math.round((group.totalSaved / group.targetAmount) * 100)}%</span>
                    </div>
                    <Progress value={(group.totalSaved / group.targetAmount) * 100} />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>USD {group.totalSaved.toLocaleString()}</span>
                      <span>USD {group.targetAmount.toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      className="flex-1"
                      onClick={() => router.push(`/group-savings/${group.id}/contribute`)}
                    >
                      Contribute
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => router.push(`/group-savings/${group.id}`)}
                    >
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <div className="flex items-center justify-between mb-3 sm:mb-4">
            <h2 className="text-lg sm:text-xl font-semibold">Recent Activity</h2>
            <Button variant="outline" size="sm" onClick={() => router.push('/group-savings/activity')}>
              View All
            </Button>
          </div>
          <div className="space-y-3">
            {recentActivity.map((activity) => (
              <Card key={activity.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-full ${
                        activity.type === "contribution" 
                          ? "bg-green-100 text-green-600" 
                          : "bg-blue-100 text-blue-600"
                      }`}>
                        {activity.type === "contribution" ? (
                          <ArrowUpIcon className="h-4 w-4" />
                        ) : (
                          <ArrowDownIcon className="h-4 w-4" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {activity.type === "contribution" ? "Contribution" : "Payout"} - {activity.groupName}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {activity.member} • {activity.date}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${
                        activity.type === "contribution" 
                          ? "text-green-600" 
                          : "text-blue-600"
                      }`}>
                        {activity.type === "contribution" ? "+" : "-"}USD {activity.amount.toLocaleString()}
                      </p>
                      <Badge 
                        variant={activity.status === "completed" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {activity.status}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </DashboardShell>
  )
}
