import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Helper function to safely access wallet balances
export function getWalletBalance(
  wallet: { balances: { ZWL: number; USD: number }; primaryCurrency: string } | null | undefined,
  currency?: string
) {
  if (!wallet) return 0;

  const currencyToUse = currency || wallet.primaryCurrency;
  if (currencyToUse === 'ZWL') {
    return wallet.balances.ZWL;
  }
  if (currencyToUse === 'USD') {
    return wallet.balances.USD;
  }
  return 0;
}
