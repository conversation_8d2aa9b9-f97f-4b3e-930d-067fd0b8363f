"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useUserStore } from "@/lib/stores/userStore";
import { Eye, EyeOff, Fingerprint, Smartphone, AlertCircle } from "lucide-react";

export default function LoginPage() {
  const router = useRouter();
  const { setUser } = useUserStore();
  const [loginMethod, setLoginMethod] = useState<'pin' | 'password'>('pin');
  const [userType, setUserType] = useState<'personal' | 'business' | 'agent'>('personal');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [pin, setPin] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [attemptCount, setAttemptCount] = useState(0);

  // Mock authentication function
  const handleLogin = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock validation
      if (!phoneNumber || (!pin && !password)) {
        throw new Error('Please fill in all required fields');
      }

      if (attemptCount >= 3) {
        throw new Error('Account locked due to too many failed attempts');
      }

      // Mock successful login
      const mockUser = {
        id: '1',
        name: userType === 'business' ? 'Acme Corp' : 'John Doe',
        email: '<EMAIL>',
        phone: phoneNumber,
        userType,
        avatar: '/images/avatar.png',
        preferences: {
          theme: 'light' as const,
          notifications: true,
          language: 'en'
        }
      };

      setUser(mockUser);

      // Redirect based on user type
      if (userType === 'business') {
        router.push('/business');
      } else if (userType === 'agent') {
        router.push('/agent');
      } else {
        router.push('/');
      }
    } catch (err: any) {
      setError(err.message);
      setAttemptCount(prev => prev + 1);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <header className="container flex h-16 items-center justify-between py-4">
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/images/zb-logo.png"
            alt="ZB Digital Wallet"
            width={40}
            height={40}
            className="h-8 w-auto"
          />
          <span className="font-semibold">ZBUniWallet</span>
        </Link>
        <Link href="/register">
          <Button variant="ghost">Register</Button>
        </Link>
      </header>
      <main className="container flex flex-1 items-center justify-center py-10">
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">Sign In</CardTitle>
            <CardDescription>
              Access your ZBUniWallet account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* User Type Selection */}
            <div className="space-y-2">
              <Label>Account Type</Label>
              <Select value={userType} onValueChange={(value: 'personal' | 'business' | 'agent') => setUserType(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select account type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="personal">Personal Account</SelectItem>
                  <SelectItem value="business">Business Account</SelectItem>
                  <SelectItem value="agent">Agent Account</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Phone Number */}
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                placeholder="+263 77 123 4567"
                type="tel"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                autoCapitalize="none"
                autoCorrect="off"
              />
            </div>

            {/* Login Method Tabs */}
            <Tabs value={loginMethod} onValueChange={(value: 'pin' | 'password') => setLoginMethod(value)}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="pin" className="flex items-center gap-2">
                  <Smartphone className="h-4 w-4" />
                  PIN
                </TabsTrigger>
                <TabsTrigger value="password" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Password
                </TabsTrigger>
              </TabsList>

              <TabsContent value="pin" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="pin">4-Digit PIN</Label>
                  <Input
                    id="pin"
                    type="password"
                    placeholder="••••"
                    maxLength={4}
                    value={pin}
                    onChange={(e) => setPin(e.target.value)}
                    className="text-center text-lg tracking-widest"
                  />
                </div>
              </TabsContent>

              <TabsContent value="password" className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">Password</Label>
                    <Link
                      href="/forgot-password"
                      className="text-xs text-muted-foreground underline-offset-4 hover:underline"
                    >
                      Forgot Password?
                    </Link>
                  </div>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Biometric Option */}
            {biometricAvailable && (
              <Button variant="outline" className="w-full">
                <Fingerprint className="mr-2 h-4 w-4" />
                Use Biometric Authentication
              </Button>
            )}
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Button
              className="w-full"
              onClick={handleLogin}
              disabled={isLoading || attemptCount >= 3}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>

            {attemptCount >= 3 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Account locked. Please contact support or try again later.
                </AlertDescription>
              </Alert>
            )}

            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
              <div className="absolute inset-x-0 top-1/2 h-px -translate-y-1/2 bg-muted" />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Button variant="outline" disabled={isLoading}>Google</Button>
              <Button variant="outline" disabled={isLoading}>Facebook</Button>
            </div>

            <p className="text-center text-sm text-muted-foreground">
              Don't have an account?{" "}
              <Link
                href="/register"
                className="font-medium text-primary underline underline-offset-4 hover:text-primary/90"
              >
                Create one
              </Link>
            </p>
          </CardFooter>
        </Card>
      </main>
      <footer className="border-t bg-muted/50 py-6 text-center text-sm text-muted-foreground">
        <div className="container">
          &copy; {new Date().getFullYear()} ZB Financial Holdings. All rights reserved.
        </div>
      </footer>
    </div>
  );
}
