import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

export default function LoginPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="container flex h-16 items-center justify-between py-4">
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/images/zb-logo.png"
            alt="ZB Digital Wallet"
            width={40}
            height={40}
            className="h-8 w-auto"
          />
          <span className="font-semibold">ZB Digital Wallet</span>
        </Link>
        <Link href="/register">
          <Button variant="ghost">Register</Button>
        </Link>
      </header>
      <main className="container flex flex-1 items-center justify-center py-10">
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">Sign In</CardTitle>
            <CardDescription>
              Enter your credentials to access your account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email or Phone Number</Label>
              <Input
                id="email"
                placeholder="<EMAIL> or 07XXXXXXXX"
                type="text"
                autoCapitalize="none"
                autoCorrect="off"
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link
                  href="/forgot-password"
                  className="text-xs text-muted-foreground underline-offset-4 hover:underline"
                >
                  Forgot Password?
                </Link>
              </div>
              <Input id="password" type="password" />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="remember"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label
                htmlFor="remember"
                className="text-sm font-normal text-muted-foreground"
              >
                Remember me for 30 days
              </Label>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Link href="/" className="w-full">
              <Button className="w-full">Sign In</Button>
            </Link>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
              <div className="absolute inset-x-0 top-1/2 h-px -translate-y-1/2 bg-muted" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Button variant="outline">Google</Button>
              <Button variant="outline">Facebook</Button>
            </div>
            <p className="text-center text-sm text-muted-foreground">
              Don't have an account?{" "}
              <Link
                href="/register"
                className="font-medium text-primary underline underline-offset-4 hover:text-primary/90"
              >
                Create one
              </Link>
            </p>
          </CardFooter>
        </Card>
      </main>
      <footer className="border-t bg-muted/50 py-6 text-center text-sm text-muted-foreground">
        <div className="container">
          &copy; {new Date().getFullYear()} ZB Financial Holdings. All rights reserved.
        </div>
      </footer>
    </div>
  );
}
