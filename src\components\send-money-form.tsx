import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { WalletSelectorTabs } from './wallet-selector-tabs'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'

export function SendMoneyForm() {
  const router = useRouter()
  const [selectedWalletId, setSelectedWalletId] = useState<string>()
  const [amount, setAmount] = useState('')
  const [recipient, setRecipient] = useState('')

  const handleAddWallet = () => {
    router.push('/wallets')
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>Send Money</CardTitle>
          <CardDescription>Choose a wallet to send money from</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <WalletSelectorTabs
            selectedWalletId={selectedWalletId}
            onWalletSelect={setSelectedWalletId}
            onAddWallet={handleAddWallet}
          />

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="recipient">Recipient</Label>
              <Input
                id="recipient"
                placeholder="Enter recipient's phone number or account"
                value={recipient}
                onChange={(e) => setRecipient(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Enter amount to send"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
              />
            </div>

            <Button
              className="w-full"
              disabled={!selectedWalletId || !amount || !recipient}
            >
              Send Money
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
} 