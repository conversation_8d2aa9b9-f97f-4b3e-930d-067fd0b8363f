{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "export": "next export", "start": "next start", "lint": "next lint", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "next": "14.1.0", "next-themes": "^0.2.1", "react": "^18", "react-day-picker": "^9.6.3", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "sonner": "^2.0.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^4.5.2", "@anthropic-ai/sdk": "^0.39.0", "boxen": "^8.0.1", "chalk": "^4.1.2", "commander": "^11.1.0", "cli-table3": "^0.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "openai": "^4.89.0", "ora": "^8.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "rimraf": "^5.0.10", "tailwindcss": "^3.3.0", "typescript": "^5"}, "type": "module"}