import Image from 'next/image'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { Wallet } from '@/lib/stores/wallet-store'
import { useRouter } from 'next/navigation'
import { 
  ArrowUpRight, 
  ArrowDown, 
  Wallet as WalletIcon, 
  Send, 
  Download,
  CheckCircle2, 
  Clock, 
  XCircle, 
  AlertCircle,
  LucideIcon
} from 'lucide-react'
import { motion } from "framer-motion"
import { BalanceDisplay } from './BalanceDisplay'
import { useWalletStore } from '@/lib/stores/wallet-store'

interface StateConfig {
  icon: LucideIcon
  color: string
  badgeClass: string
  label: string
}

const stateConfig: Record<Wallet["state"], StateConfig> = {
  connected: {
    icon: CheckCircle2,
    color: "text-green-500",
    badgeClass: "bg-green-100 text-green-800 border-green-300",
    label: "Connected"
  },
  pending: {
    icon: Clock,
    color: "text-yellow-500",
    badgeClass: "bg-yellow-100 text-yellow-800 border-yellow-300",
    label: "Pending"
  },
  disconnected: {
    icon: XCircle,
    color: "text-red-500",
    badgeClass: "bg-red-100 text-red-800 border-red-300",
    label: "Disconnected"
  },
  suspended: {
    icon: AlertCircle,
    color: "text-orange-500",
    badgeClass: "bg-orange-100 text-orange-800 border-orange-300",
    label: "Suspended"
  }
};

interface WalletCardProps {
  wallet: Wallet
  onSendMoney: (walletId: string) => void
  onReceiveMoney: () => void
  showActions?: boolean
}

export function WalletCard({ wallet, onSendMoney, onReceiveMoney, showActions = true }: WalletCardProps) {
  const router = useRouter()

  return (
    <Card className="relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10" />
      <div className="relative p-6">
        <div className="flex items-center gap-3 mb-3">
          <div className="relative h-10 w-10">
            <Image
              src={wallet.logo}
              alt={wallet.name}
              fill
              className="object-contain"
            />
          </div>
          <div>
            <h3 className="font-medium">{wallet.name}</h3>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={cn(
                "text-xs",
                stateConfig[wallet.state].badgeClass
              )}>
                {stateConfig[wallet.state].label}
              </Badge>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-between mb-4">
          <div className="text-sm text-muted-foreground">Available Balance</div>
          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium">
              ZWL {wallet.balances['ZWL'].toLocaleString()}
            </span>
            <span className="text-muted-foreground">|</span>
            <span className="font-medium">
              USD {wallet.balances['USD'].toLocaleString()}
            </span>
          </div>
        </div>
        {showActions && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => onSendMoney(wallet.id)}
            >
              <Send className="mr-2 h-4 w-4" />
              Send
            </Button>
            <Button
              variant="outline"
              className="flex-1"
              onClick={onReceiveMoney}
            >
              <Download className="mr-2 h-4 w-4" />
              Receive
            </Button>
          </div>
        )}
      </div>
    </Card>
  )
} 