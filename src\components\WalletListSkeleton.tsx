import { Card } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export function WalletListSkeleton() {
  return (
    <div className="space-y-6 font-rubik">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <Skeleton className="h-8 w-[150px]" />
          <Skeleton className="h-4 w-[250px]" />
        </div>
        <div className="flex items-center space-x-2">
          <Skeleton className="h-10 w-[60px]" />
          <Skeleton className="h-10 w-[60px]" />
        </div>
      </div>

      <Skeleton className="h-10 w-[300px]" />

      <div className="relative">
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card
              key={index}
              className="flex-shrink-0 w-[300px] p-6 space-y-4"
            >
              <div className="flex items-center justify-between">
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-2 w-2 rounded-full" />
                  <Skeleton className="h-4 w-20" />
                </div>
              </div>

              <div>
                <Skeleton className="h-5 w-24 mb-2" />
                <Skeleton className="h-8 w-32" />
              </div>

              <div className="flex items-center space-x-2">
                <Skeleton className="h-10 flex-1" />
                <Skeleton className="h-10 flex-1" />
                <Skeleton className="h-10 flex-1" />
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
} 