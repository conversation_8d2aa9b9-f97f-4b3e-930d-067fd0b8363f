# 2. Monitoring and Alerting

## 📊 **Monitoring and Alerting Overview**

This document provides comprehensive guidelines for monitoring and alerting systems for the UniversalWallet platform, covering infrastructure monitoring, application performance monitoring, business metrics tracking, and enterprise-level observability.

## 🏗️ **Monitoring Architecture**

### **Observability Stack**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Applications  │    │  Infrastructure │    │   Business      │
│   - Logs        │    │  - Metrics      │    │   - KPIs        │
│   - Metrics     │    │  - Events       │    │   - SLAs        │
│   - Traces      │    │  - Health       │    │   - Alerts      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Prometheus    │    │   CloudWatch    │    │   Custom        │
│   - Metrics     │    │   - AWS Metrics │    │   - Business    │
│   - Alerting    │    │   - Logs        │    │   - Analytics   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │     Grafana     │
                    │   - Dashboards  │
                    │   - Alerts      │
                    │   - Reports     │
                    └─────────────────┘
```

### **Monitoring Components**
- **Metrics Collection**: Prometheus, CloudWatch, Custom metrics
- **Log Aggregation**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Distributed Tracing**: <PERSON><PERSON><PERSON>, <PERSON><PERSON> X-Ray
- **Visualization**: <PERSON><PERSON>, CloudWatch Dashboards
- **Alerting**: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, Slack
- **Synthetic Monitoring**: <PERSON><PERSON>, DataDog Synthetics

---

## 📈 **Application Performance Monitoring**

### **Spring Boot Metrics Configuration**
```java
// src/main/java/com/universalwallet/config/MetricsConfig.java
@Configuration
@EnableConfigurationProperties(MetricsProperties.class)
public class MetricsConfig {
    
    @Bean
    public MeterRegistry meterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }
    
    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }
    
    @Bean
    public CountedAspect countedAspect(MeterRegistry registry) {
        return new CountedAspect(registry);
    }
    
    @Bean
    @ConditionalOnMissingBean
    public PrometheusMeterRegistry prometheusMeterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }
    
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config()
            .commonTags("application", "universalwallet")
            .commonTags("environment", "${spring.profiles.active}")
            .commonTags("version", "${app.version}");
    }
}
```

### **Custom Business Metrics**
```java
// src/main/java/com/universalwallet/metrics/BusinessMetrics.java
@Component
@Slf4j
public class BusinessMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter transactionCounter;
    private final Timer transactionTimer;
    private final Gauge activeUsersGauge;
    private final Counter failedTransactionCounter;
    
    public BusinessMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        this.transactionCounter = Counter.builder("transactions.total")
            .description("Total number of transactions")
            .tag("type", "all")
            .register(meterRegistry);
            
        this.transactionTimer = Timer.builder("transaction.duration")
            .description("Transaction processing time")
            .register(meterRegistry);
            
        this.activeUsersGauge = Gauge.builder("users.active")
            .description("Number of active users")
            .register(meterRegistry, this, BusinessMetrics::getActiveUserCount);
            
        this.failedTransactionCounter = Counter.builder("transactions.failed")
            .description("Number of failed transactions")
            .register(meterRegistry);
    }
    
    public void recordTransaction(TransactionType type, BigDecimal amount, Duration duration) {
        transactionCounter.increment(
            Tags.of(
                "type", type.name().toLowerCase(),
                "amount_range", getAmountRange(amount)
            )
        );
        
        transactionTimer.record(duration);
        
        log.debug("Recorded transaction metric: type={}, amount={}, duration={}ms", 
            type, amount, duration.toMillis());
    }
    
    public void recordFailedTransaction(TransactionType type, String errorCode) {
        failedTransactionCounter.increment(
            Tags.of(
                "type", type.name().toLowerCase(),
                "error_code", errorCode
            )
        );
        
        log.warn("Recorded failed transaction: type={}, error={}", type, errorCode);
    }
    
    public void recordUserActivity(String activity, UUID userId) {
        Counter.builder("user.activity")
            .description("User activity events")
            .tag("activity", activity)
            .tag("user_type", getUserType(userId))
            .register(meterRegistry)
            .increment();
    }
    
    private String getAmountRange(BigDecimal amount) {
        if (amount.compareTo(new BigDecimal("100")) <= 0) return "small";
        if (amount.compareTo(new BigDecimal("1000")) <= 0) return "medium";
        if (amount.compareTo(new BigDecimal("10000")) <= 0) return "large";
        return "very_large";
    }
    
    private double getActiveUserCount() {
        // Implementation to get active user count
        return userService.getActiveUserCount();
    }
}
```

### **Prometheus ServiceMonitor Configuration**
```yaml
# k8s/monitoring/service-monitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: universalwallet-backend
  namespace: monitoring
  labels:
    app: universalwallet
spec:
  selector:
    matchLabels:
      app: universalwallet-backend
  endpoints:
  - port: management
    path: /actuator/prometheus
    interval: 30s
    scrapeTimeout: 10s
  namespaceSelector:
    matchNames:
    - application
```

---

## 🚨 **Alerting Configuration**

### **Prometheus Alert Rules**
```yaml
# k8s/monitoring/alert-rules.yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: universalwallet-alerts
  namespace: monitoring
  labels:
    app: universalwallet
spec:
  groups:
  - name: universalwallet.critical
    interval: 30s
    rules:
    - alert: HighErrorRate
      expr: |
        (
          rate(http_requests_total{status=~"5.."}[5m]) /
          rate(http_requests_total[5m])
        ) > 0.05
      for: 2m
      labels:
        severity: critical
        service: universalwallet-backend
      annotations:
        summary: "High error rate detected"
        description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
        runbook_url: "https://docs.universalwallet.co.zw/runbooks/high-error-rate"

    - alert: TransactionProcessingFailure
      expr: |
        rate(transactions_failed_total[5m]) > 0.1
      for: 1m
      labels:
        severity: critical
        service: transaction-processing
      annotations:
        summary: "High transaction failure rate"
        description: "Transaction failure rate is {{ $value }} per second"
        runbook_url: "https://docs.universalwallet.co.zw/runbooks/transaction-failures"

    - alert: DatabaseConnectionFailure
      expr: |
        hikaricp_connections_active / hikaricp_connections_max > 0.9
      for: 2m
      labels:
        severity: critical
        service: database
      annotations:
        summary: "Database connection pool exhaustion"
        description: "Database connection pool is {{ $value | humanizePercentage }} full"

    - alert: HighResponseTime
      expr: |
        histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
      for: 5m
      labels:
        severity: warning
        service: universalwallet-backend
      annotations:
        summary: "High response time detected"
        description: "95th percentile response time is {{ $value }}s"

  - name: universalwallet.business
    interval: 60s
    rules:
    - alert: LowTransactionVolume
      expr: |
        rate(transactions_total[1h]) < 10
      for: 10m
      labels:
        severity: warning
        service: business-metrics
      annotations:
        summary: "Low transaction volume"
        description: "Transaction rate is {{ $value }} per second, below expected threshold"

    - alert: ExternalServiceDown
      expr: |
        up{job="external-services"} == 0
      for: 1m
      labels:
        severity: critical
        service: external-integration
      annotations:
        summary: "External service unavailable"
        description: "External service {{ $labels.instance }} is down"

    - alert: HighMemoryUsage
      expr: |
        (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
      for: 5m
      labels:
        severity: warning
        service: infrastructure
      annotations:
        summary: "High memory usage"
        description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"
```

### **AlertManager Configuration**
```yaml
# k8s/monitoring/alertmanager.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.gmail.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: '${SMTP_PASSWORD}'

    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      routes:
      - match:
          severity: critical
        receiver: 'critical-alerts'
        group_wait: 0s
        repeat_interval: 5m
      - match:
          severity: warning
        receiver: 'warning-alerts'
        repeat_interval: 30m

    receivers:
    - name: 'default'
      email_configs:
      - to: '<EMAIL>'
        subject: 'UniversalWallet Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          {{ end }}

    - name: 'critical-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: UniversalWallet Alert'
        body: |
          CRITICAL ALERT TRIGGERED
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
      
      pagerduty_configs:
      - service_key: '${PAGERDUTY_SERVICE_KEY}'
        description: 'Critical alert: {{ .GroupLabels.alertname }}'
        
      slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#alerts-critical'
        title: 'Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}

    - name: 'warning-alerts'
      slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#alerts-warning'
        title: 'Warning: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Service:* {{ .Labels.service }}
          {{ end }}
```

---

## 📊 **Grafana Dashboards**

### **Application Performance Dashboard**
```json
{
  "dashboard": {
    "id": null,
    "title": "UniversalWallet - Application Performance",
    "tags": ["universalwallet", "performance"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total{job=\"universalwallet-backend\"}[5m]))",
            "legendFormat": "Requests/sec"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "reqps",
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 100},
                {"color": "red", "value": 500}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "Error Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total{job=\"universalwallet-backend\",status=~\"5..\"}[5m])) / sum(rate(http_requests_total{job=\"universalwallet-backend\"}[5m]))",
            "legendFormat": "Error Rate"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percentunit",
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 0.01},
                {"color": "red", "value": 0.05}
              ]
            }
          }
        }
      },
      {
        "id": 3,
        "title": "Response Time (95th percentile)",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job=\"universalwallet-backend\"}[5m])) by (le))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{job=\"universalwallet-backend\"}[5m])) by (le))",
            "legendFormat": "50th percentile"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "s"
          }
        }
      },
      {
        "id": 4,
        "title": "Transaction Volume",
        "type": "timeseries",
        "targets": [
          {
            "expr": "sum(rate(transactions_total[5m])) by (type)",
            "legendFormat": "{{ type }}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "tps"
          }
        }
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

### **Business Metrics Dashboard**
```json
{
  "dashboard": {
    "id": null,
    "title": "UniversalWallet - Business Metrics",
    "tags": ["universalwallet", "business"],
    "panels": [
      {
        "id": 1,
        "title": "Daily Transaction Volume",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(increase(transactions_total[24h]))",
            "legendFormat": "Transactions Today"
          }
        ]
      },
      {
        "id": 2,
        "title": "Transaction Success Rate",
        "type": "gauge",
        "targets": [
          {
            "expr": "sum(rate(transactions_total{status=\"completed\"}[1h])) / sum(rate(transactions_total[1h]))",
            "legendFormat": "Success Rate"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percentunit",
            "min": 0,
            "max": 1,
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 0.95},
                {"color": "green", "value": 0.99}
              ]
            }
          }
        }
      },
      {
        "id": 3,
        "title": "Active Users",
        "type": "timeseries",
        "targets": [
          {
            "expr": "users_active",
            "legendFormat": "Active Users"
          }
        ]
      },
      {
        "id": 4,
        "title": "Revenue by Transaction Type",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum(transaction_revenue_total) by (type)",
            "legendFormat": "{{ type }}"
          }
        ]
      }
    ]
  }
}
```

---

## 🔍 **Log Management**

### **Structured Logging Configuration**
```java
// src/main/resources/logback-spring.xml
<configuration>
    <springProfile name="production">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <arguments/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/app/logs/universalwallet.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/app/logs/universalwallet.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <arguments/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="FILE"/>
        </root>
        
        <logger name="com.universalwallet" level="DEBUG"/>
        <logger name="org.springframework.security" level="DEBUG"/>
        <logger name="org.springframework.web" level="INFO"/>
    </springProfile>
</configuration>
```

**This comprehensive monitoring and alerting system ensures proactive detection and resolution of issues, maintaining high availability and performance for the UniversalWallet platform.** 📊
