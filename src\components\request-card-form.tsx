"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>oot<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import { wallets } from "@/lib/data";
import { Separator } from "@/components/ui/separator";

export function RequestCardForm() {
  const [cardType, setCardType] = useState<"visa" | "mastercard" | "uniCard">("uniCard");
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Request a Virtual Card</CardTitle>
        <CardDescription>
          Get a virtual card to make online payments securely
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label>Card Type</Label>
          <Tabs
            defaultValue="uniCard"
            className="w-full"
            onValueChange={(value) => setCardType(value as "visa" | "mastercard" | "uniCard")}
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="uniCard" className="flex items-center justify-center gap-2">
                <div className="w-8 h-5 relative">
                  <Image
                    src="/images/zb-logo.png"
                    alt="ZB UniCard"
                    width={32}
                    height={20}
                    className="h-5 w-8 object-contain"
                  />
                </div>
                <span>ZB UniCard</span>
              </TabsTrigger>
              <TabsTrigger value="visa" className="flex items-center justify-center gap-2">
                <div className="w-8 h-5 relative">
                  <Image
                    src="/images/visa-logo.png"
                    alt="Visa"
                    width={32}
                    height={20}
                    className="h-5 w-8 object-contain"
                  />
                </div>
                <span>Visa</span>
              </TabsTrigger>
              <TabsTrigger value="mastercard" className="flex items-center justify-center gap-2">
                <div className="w-8 h-5 relative">
                  <Image
                    src="/images/mastercard-logo.png"
                    alt="Mastercard"
                    width={32}
                    height={20}
                    className="h-5 w-8 object-contain"
                  />
                </div>
                <span>Mastercard</span>
              </TabsTrigger>
            </TabsList>
            <TabsContent value="uniCard" className="mt-4">
              <div className="p-4 border rounded-lg bg-gradient-to-r from-zb-green/10 to-zb-lime/10">
                <h4 className="font-medium text-zb-green">ZB UniCard Benefits</h4>
                <ul className="mt-2 space-y-1 text-sm text-zb-green/90">
                  <li>• Zero transaction fees for all ZB to ZB transfers</li>
                  <li>• International payments with real-time exchange rates</li>
                  <li>• Instant transfers to any linked wallet</li>
                  <li>• Advanced security with biometric authentication</li>
                  <li>• Free card issuance</li>
                </ul>
              </div>
            </TabsContent>
            <TabsContent value="visa" className="mt-4">
              <div className="p-4 border rounded-lg bg-blue-50">
                <h4 className="font-medium text-blue-700">Visa Virtual Card Benefits</h4>
                <ul className="mt-2 space-y-1 text-sm text-blue-600">
                  <li>• Accepted by millions of merchants worldwide</li>
                  <li>• Enhanced security features for online transactions</li>
                  <li>• Free card issuance</li>
                  <li>• 2.5% transaction fee on international purchases</li>
                </ul>
              </div>
            </TabsContent>
            <TabsContent value="mastercard" className="mt-4">
              <div className="p-4 border rounded-lg bg-orange-50">
                <h4 className="font-medium text-orange-700">Mastercard Virtual Card Benefits</h4>
                <ul className="mt-2 space-y-1 text-sm text-orange-600">
                  <li>• Global acceptance at millions of locations</li>
                  <li>• Advanced fraud protection</li>
                  <li>• ZWL 50 card issuance fee</li>
                  <li>• 2% transaction fee on international purchases</li>
                </ul>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-2">
          <Label>Link to Wallet</Label>
          <div className="grid gap-4">
            {wallets.filter(wallet => wallet.isConnected).map((wallet) => (
              <div
                key={wallet.id}
                className={`flex items-center justify-between rounded-lg border p-4 cursor-pointer hover:bg-muted/50 ${
                  selectedWallet === wallet.id ? "border-primary bg-primary/5" : ""
                }`}
                onClick={() => setSelectedWallet(wallet.id)}
              >
                <div className="flex items-center gap-3">
                  <div className="rounded-md bg-primary/10 p-2">
                    <Image
                      src={wallet.logo}
                      alt={wallet.logoAlt}
                      width={32}
                      height={32}
                      className="h-8 w-8"
                    />
                  </div>
                  <div>
                    <div className="font-medium">{wallet.name}</div>
                    <div className="text-sm text-muted-foreground">
                      Balance: {wallet.primaryCurrency} {wallet.balances[wallet.primaryCurrency].toLocaleString(undefined, { minimumFractionDigits: 2 })}
                    </div>
                  </div>
                </div>
                <div className={`h-4 w-4 rounded-full border ${
                  selectedWallet === wallet.id ? "bg-primary border-primary" : "border-muted-foreground"
                }`} />
              </div>
            ))}
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="card-name">Name on Card</Label>
              <Input id="card-name" placeholder="e.g. John Doe" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="card-limit">Card Limit (Optional)</Label>
              <Input id="card-limit" type="number" placeholder="ZWL 0.00" />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Card Purpose (Optional)</Label>
            <select
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
            >
              <option value="">Select a purpose</option>
              <option value="shopping">Online Shopping</option>
              <option value="subscriptions">Subscriptions</option>
              <option value="travel">Travel</option>
              <option value="business">Business Expenses</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <div className="text-sm text-muted-foreground">
          <span className="font-medium text-foreground">Fee:</span> {
            cardType === "visa" ? "Free" :
            cardType === "mastercard" ? "ZWL 50.00" :
            "Free (Premium)"
          }
        </div>
        <Button>Request Card</Button>
      </CardFooter>
    </Card>
  );
}
