'use client'

import { useWalletStore } from '@/lib/stores/wallet-store'
import type { Wallet, WalletType, WalletConnectionState } from '@/lib/stores/wallet-store'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useState, useEffect } from 'react'
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { WalletListSkeleton } from './WalletListSkeleton'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChevronDown, Wallet as WalletIcon, Plus } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { WalletCard } from './WalletCard'
import { Card } from '@/components/ui/card'
import { NewRequestModal } from './NewRequestModal'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { TopUpModal } from './TopUpModal'

type WalletStateFilter = 'all' | WalletConnectionState
type WalletTypeFilter = 'all' | WalletType

export function WalletList() {
  const router = useRouter()
  const { 
    wallets, 
    setSelectedWallet,
    selectedCurrency,
    setSelectedCurrency
  } = useWalletStore()
  const [selectedType, setSelectedType] = useState<WalletTypeFilter>('all')
  const [selectedState, setSelectedState] = useState<WalletStateFilter>('connected')
  const [isLoading, setIsLoading] = useState(true)
  const [showNewRequest, setShowNewRequest] = useState(false)
  const [showTopUp, setShowTopUp] = useState(false)
  const [selectedWalletForTopUp, setSelectedWalletForTopUp] = useState<{
    id: string
    name: string
    logo: string
    type: string
  } | null>(null)

  useEffect(() => {
    // Simulate loading delay
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  const filteredWallets = wallets.filter(wallet => {
    if (selectedType !== 'all' && wallet.type !== selectedType) return false
    if (selectedState !== 'all' && wallet.state !== selectedState) return false
    return true
  })

  const handleSendMoney = (walletId: string) => {
    setSelectedWallet(walletId)
    router.push('/send-money')
  }

  const handleReceiveMoney = () => {
    setShowNewRequest(true)
  }

  const handleTopUp = (wallet: {
    id: string
    name: string
    logo: string
    type: string
  }) => {
    setSelectedWalletForTopUp(wallet)
    setShowTopUp(true)
  }

  if (isLoading) {
    return <WalletListSkeleton />
  }

  return (
    <div className="space-y-6">
      <NewRequestModal
        open={showNewRequest}
        onOpenChange={setShowNewRequest}
      />

      {selectedWalletForTopUp && (
        <TopUpModal
          open={showTopUp}
          onOpenChange={(open) => {
            setShowTopUp(open)
            if (!open) setSelectedWalletForTopUp(null)
          }}
          destinationWallet={selectedWalletForTopUp}
        />
      )}

      <div className="flex items-center justify-between">
        <Tabs defaultValue="all" className="w-full" onValueChange={(value) => setSelectedType(value as WalletTypeFilter)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all" className="text-xs sm:text-sm">All</TabsTrigger>
            <TabsTrigger value="mobile" className="text-xs sm:text-sm">Mobile</TabsTrigger>
            <TabsTrigger value="bank" className="text-xs sm:text-sm">Bank</TabsTrigger>
          </TabsList>
        </Tabs>

        <Select value={selectedState} onValueChange={(value) => setSelectedState(value as WalletStateFilter)}>
          <SelectTrigger className="w-[180px] ml-4 hidden sm:flex">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="connected">Active</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="suspended">Suspended</SelectItem>
            <SelectItem value="disconnected">Deleted</SelectItem>
            <SelectItem value="all">All Status</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {filteredWallets.map((wallet) => (
          <motion.div
            key={wallet.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="h-12 w-12 relative bg-white rounded-lg p-2 flex items-center justify-center">
                  <Image
                    src={wallet.logo}
                    alt={wallet.name}
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "h-2 w-2 rounded-full",
                    wallet.state === 'connected' && "bg-green-500",
                    wallet.state === 'pending' && "bg-yellow-500",
                    wallet.state === 'disconnected' && "bg-red-500",
                    wallet.state === 'suspended' && "bg-gray-500"
                  )} />
                  <span className="text-sm text-muted-foreground capitalize">{wallet.state}</span>
                </div>
              </div>
              <h3 className="font-medium mb-2">{wallet.name}</h3>
              <div className="space-y-1 mb-4">
                <p className="text-sm text-muted-foreground">
                  ZWL: {wallet.balances.ZWL.toLocaleString()}
                </p>
                <p className="text-sm text-muted-foreground">
                  USD: {wallet.balances.USD.toLocaleString()}
                </p>
              </div>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleSendMoney(wallet.id)}
                >
                  Send
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={handleReceiveMoney}
                >
                  Receive
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleTopUp({
                    id: wallet.id,
                    name: wallet.name,
                    logo: wallet.logo,
                    type: wallet.type
                  })}
                >
                  Top Up
                </Button>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  )
} 