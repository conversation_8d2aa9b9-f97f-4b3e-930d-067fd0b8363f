'use client'

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card } from "@/components/ui/card"
import { ChevronDown } from "lucide-react"
import Image from "next/image"
import { useWalletStore } from "@/lib/stores/wallet-store"
import { useState } from "react"

export function WalletSelector() {
  const { wallets, selectedWallet, setSelectedWallet } = useWalletStore()
  const [selectedCurrency, setSelectedCurrency] = useState<"ZWL" | "USD">("ZWL")

  const selectedWalletData = wallets.find(wallet => wallet.id === selectedWallet)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="w-[200px] justify-between bg-muted/30 hover:bg-muted/50 border-0 h-auto p-4"
        >
          {selectedWalletData ? (
            <div className="flex items-center gap-3">
              <div className="h-8 w-8 relative">
                <Image
                  src={selectedWalletData.logo}
                  alt={selectedWalletData.name}
                  fill
                  className="object-contain"
                />
              </div>
              <span>{selectedWalletData.name}</span>
            </div>
          ) : (
            <span>Select a wallet</span>
          )}
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        {wallets
          .filter(wallet => wallet.isConnected)
          .map(wallet => (
            <DropdownMenuItem
              key={wallet.id}
              onClick={() => setSelectedWallet(wallet.id)}
              className="p-3"
            >
              <div className="flex items-center gap-3">
                <div className="h-8 w-8 relative">
                  <Image
                    src={wallet.logo}
                    alt={wallet.name}
                    fill
                    className="object-contain"
                  />
                </div>
                <span>{wallet.name}</span>
              </div>
            </DropdownMenuItem>
          ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 