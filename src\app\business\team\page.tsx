"use client";

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { DashboardShell } from '@/components/dashboard-shell'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Users,
  Shield,
  Mail,
  Phone,
  Calendar,
  Settings,
  UserPlus,
  Edit,
  Trash2
} from 'lucide-react'

export default function TeamManagementPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')

  // Mock team data
  const teamMembers = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+263771234567',
      role: 'Admin',
      department: 'Finance',
      status: 'active',
      joinDate: '2023-01-15',
      lastLogin: '2024-01-20 09:30',
      permissions: ['payments', 'reporting', 'user_management', 'settings'],
      avatar: '/images/team/sarah.png'
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+************',
      role: 'Manager',
      department: 'Operations',
      status: 'active',
      joinDate: '2023-03-20',
      lastLogin: '2024-01-19 16:45',
      permissions: ['payments', 'reporting', 'bulk_payments'],
      avatar: '/images/team/mike.png'
    },
    {
      id: '3',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+************',
      role: 'Operator',
      department: 'Accounts',
      status: 'active',
      joinDate: '2023-06-10',
      lastLogin: '2024-01-20 11:15',
      permissions: ['payments', 'invoicing'],
      avatar: '/images/team/jane.png'
    },
    {
      id: '4',
      name: 'David Brown',
      email: '<EMAIL>',
      phone: '+************',
      role: 'Viewer',
      department: 'Sales',
      status: 'inactive',
      joinDate: '2023-09-05',
      lastLogin: '2024-01-10 14:20',
      permissions: ['reporting'],
      avatar: '/images/team/david.png'
    }
  ]

  const teamStats = {
    total: teamMembers.length,
    active: teamMembers.filter(member => member.status === 'active').length,
    inactive: teamMembers.filter(member => member.status === 'inactive').length,
    admins: teamMembers.filter(member => member.role === 'Admin').length,
    managers: teamMembers.filter(member => member.role === 'Manager').length,
    operators: teamMembers.filter(member => member.role === 'Operator').length
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'Admin': return 'bg-red-100 text-red-800'
      case 'Manager': return 'bg-blue-100 text-blue-800'
      case 'Operator': return 'bg-green-100 text-green-800'
      case 'Viewer': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    return status === 'active' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-gray-100 text-gray-800'
  }

  const filteredMembers = teamMembers.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.department.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <DashboardShell>
      <div className="container px-4 sm:px-6 space-y-6 sm:space-y-8 pb-20 md:pb-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Team Management</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage team members, roles, and permissions
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Roles & Permissions
            </Button>
            <Button onClick={() => router.push('/business/team/invite')}>
              <UserPlus className="h-4 w-4 mr-2" />
              Invite Member
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{teamStats.total}</div>
              <p className="text-xs text-muted-foreground">{teamStats.active} active</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Administrators</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{teamStats.admins}</div>
              <p className="text-xs text-muted-foreground">Full access</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Managers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{teamStats.managers}</div>
              <p className="text-xs text-muted-foreground">Limited admin access</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Operators</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{teamStats.operators}</div>
              <p className="text-xs text-muted-foreground">Operational access</p>
            </CardContent>
          </Card>
        </div>

        {/* Team Members */}
        <Card>
          <CardHeader>
            <CardTitle>Team Members</CardTitle>
            <CardDescription>Manage your team members and their access levels</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search team members..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Team Member List */}
            <div className="space-y-4">
              {filteredMembers.map((member) => (
                <div key={member.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      
                      <div>
                        <h3 className="font-semibold">{member.name}</h3>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Mail className="h-3 w-3" />
                          <span>{member.email}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Phone className="h-3 w-3" />
                          <span>{member.phone}</span>
                        </div>
                      </div>
                      
                      <div className="hidden sm:block">
                        <p className="text-sm font-medium">{member.department}</p>
                        <p className="text-xs text-muted-foreground">Joined {member.joinDate}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="text-right hidden sm:block">
                        <p className="text-sm text-muted-foreground">Last login</p>
                        <p className="text-xs">{member.lastLogin}</p>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Badge className={getRoleColor(member.role)}>
                          {member.role}
                        </Badge>
                        <Badge className={getStatusColor(member.status)}>
                          {member.status}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Permissions */}
                  <div className="mt-4 pt-4 border-t">
                    <p className="text-sm font-medium mb-2">Permissions:</p>
                    <div className="flex flex-wrap gap-2">
                      {member.permissions.map((permission) => (
                        <Badge key={permission} variant="outline" className="text-xs">
                          {permission.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredMembers.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4" />
                <p>No team members found</p>
                <p className="text-sm">Try adjusting your search</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
