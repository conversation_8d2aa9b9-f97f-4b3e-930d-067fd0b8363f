# 5. Modular User Journeys

## 🎯 **Module-Based Journey Overview**

This document provides detailed user journeys organized by independent modules, ensuring each major feature can be implemented and scaled independently with dedicated teams while maintaining clear user experiences for each user type.

## 🏗️ **Core Business Module Journeys**

### **User Management Module Journeys**

#### **Individual User Registration Journey**
```mermaid
graph TD
    A[Download App] --> B[Start Registration]
    B --> C[Enter Phone Number]
    C --> D[Receive SMS OTP]
    D --> E[Enter OTP Code]
    E --> F[Create PIN]
    F --> G[Confirm PIN]
    G --> H[Accept Terms & Conditions]
    H --> I[Account Created]
    I --> J[Profile Setup Prompt]
    
    J --> K[Personal Details]
    K --> L[Contact Information]
    L --> M[Emergency Contact]
    M --> N[Profile Complete]
    
    N --> O[KYC Prompt]
    O --> P[Upload National ID]
    P --> Q[Selfie Verification]
    Q --> R[Address Proof]
    R --> S[KYC Submitted]
    S --> T[Verification Pending]
    T --> U[KYC Approved]
    U --> V[Full Account Access]
```

**Module Scope**: Complete within User Management Module
**Team**: User Management Team
**Dependencies**: Authentication Module (for PIN creation), Notification Module (for SMS)

#### **Business User Registration Journey**
```mermaid
graph TD
    A[Access Business Portal] --> B[Start Business Registration]
    B --> C[Business Information]
    C --> D[Business Type Selection]
    D --> E[Registration Documents]
    E --> F[Authorized Representative]
    F --> G[Business Verification]
    G --> H[Business Account Created]
    
    H --> I[Team Setup]
    I --> J[Add Team Members]
    J --> K[Assign Roles]
    K --> L[Set Permissions]
    L --> M[Team Configuration Complete]
    
    M --> N[Business KYC]
    N --> O[Business License Upload]
    O --> P[Tax Certificate]
    P --> Q[Bank Statements]
    Q --> R[Business KYC Submitted]
    R --> S[Business Verification]
    S --> T[Business Account Activated]
```

**Module Scope**: Complete within User Management Module
**Team**: User Management Team
**Dependencies**: Compliance Module (for business verification)

---

### **Payment Processing Module Journeys**

#### **Individual P2P Transfer Journey**
```mermaid
graph TD
    A[Access Transfer] --> B[Select Transfer Type]
    B --> C[P2P Transfer]
    C --> D[Enter Recipient]
    D --> E{Recipient Type}
    
    E -->|Phone Number| F[Enter Phone]
    E -->|Contact| G[Select Contact]
    E -->|QR Code| H[Scan QR]
    
    F --> I[Verify Recipient]
    G --> I
    H --> I
    
    I --> J[Enter Amount]
    J --> K[Select Source Account]
    K --> L[Review Transfer]
    L --> M[Enter PIN]
    M --> N[Process Transfer]
    N --> O[Transfer Success]
    O --> P[Receipt Generated]
    P --> Q[Share Receipt Option]
```

**Module Scope**: Complete within Payment Processing Module
**Team**: Payment Team
**Dependencies**: Account Management Module (for source accounts), Authentication Module (for PIN)

#### **Business Bulk Payment Journey**
```mermaid
graph TD
    A[Access Business Portal] --> B[Bulk Payments]
    B --> C{Payment Method}
    
    C -->|File Upload| D[Upload CSV/Excel]
    C -->|Manual Entry| E[Manual Payment Entry]
    
    D --> F[Validate File Format]
    F --> G[Review Payment List]
    E --> G
    
    G --> H[Verify Recipients]
    H --> I[Select Source Account]
    I --> J[Review Total Amount]
    J --> K[Set Processing Schedule]
    K --> L[Approval Workflow]
    L --> M{Requires Approval}
    
    M -->|Yes| N[Send for Approval]
    M -->|No| O[Process Payments]
    
    N --> P[Approval Notification]
    P --> Q[Approver Review]
    Q --> R[Approve/Reject]
    R --> O
    
    O --> S[Batch Processing]
    S --> T[Individual Payment Processing]
    T --> U[Payment Status Updates]
    U --> V[Completion Report]
```

**Module Scope**: Complete within Payment Processing Module
**Team**: Payment Team
**Dependencies**: Business Services Module (for approval workflows), Notification Module (for status updates)

---

### **Group Savings Module Journeys**

#### **Group Creation Journey**
```mermaid
graph TD
    A[Access Group Savings] --> B[Create New Group]
    B --> C[Group Basic Info]
    C --> D[Group Name & Description]
    D --> E[Savings Goal Setting]
    E --> F[Target Amount]
    F --> G[Target Date]
    G --> H[Contribution Rules]
    H --> I[Contribution Frequency]
    I --> J[Minimum Contribution]
    J --> K[Group Privacy Settings]
    K --> L[Member Invitation Rules]
    L --> M[Group Terms & Conditions]
    M --> N[Create Group]
    N --> O[Group Created Successfully]
    
    O --> P[Invite Members]
    P --> Q{Invitation Method}
    
    Q -->|Phone Numbers| R[Enter Phone Numbers]
    Q -->|Contacts| S[Select Contacts]
    Q -->|Share Code| T[Generate Group Code]
    
    R --> U[Send Invitations]
    S --> U
    T --> V[Share Group Code]
    
    U --> W[Track Invitation Status]
    V --> W
    W --> X[Group Ready]
```

**Module Scope**: Complete within Group Savings Module
**Team**: Group Savings Team
**Dependencies**: User Management Module (for member verification), Notification Module (for invitations)

#### **Group Participation Journey**
```mermaid
graph TD
    A[Receive Group Invitation] --> B[Review Group Details]
    B --> C[Group Goals & Rules]
    C --> D[Member List]
    D --> E[Contribution Requirements]
    E --> F{Join Decision}
    
    F -->|Accept| G[Accept Invitation]
    F -->|Decline| H[Decline Invitation]
    
    G --> I[Set Up Contribution]
    I --> J[Select Payment Source]
    J --> K[Set Contribution Amount]
    K --> L[Choose Contribution Schedule]
    L --> M[Confirm Participation]
    M --> N[Member Added to Group]
    
    N --> O[First Contribution]
    O --> P[Make Contribution]
    P --> Q[Contribution Processed]
    Q --> R[Group Progress Updated]
    R --> S[Contribution Confirmation]
    
    S --> T[Ongoing Participation]
    T --> U[View Group Progress]
    U --> V[Group Communication]
    V --> W[Goal Achievement Tracking]
```

**Module Scope**: Complete within Group Savings Module
**Team**: Group Savings Team
**Dependencies**: Payment Processing Module (for contributions), Notification Module (for updates)

---

### **Business Services Module Journeys**

#### **Invoice Management Journey**
```mermaid
graph TD
    A[Access Business Portal] --> B[Invoice Management]
    B --> C[Create New Invoice]
    C --> D[Customer Information]
    D --> E[Invoice Details]
    E --> F[Add Line Items]
    F --> G[Calculate Totals]
    G --> H[Payment Terms]
    H --> I[Invoice Preview]
    I --> J[Send Invoice]
    
    J --> K{Delivery Method}
    K -->|Email| L[Send via Email]
    K -->|SMS| M[Send via SMS]
    K -->|Portal| N[Customer Portal Access]
    
    L --> O[Invoice Sent]
    M --> O
    N --> O
    
    O --> P[Track Invoice Status]
    P --> Q[Payment Received]
    Q --> R[Automatic Reconciliation]
    R --> S[Invoice Marked Paid]
    S --> T[Payment Confirmation]
    
    T --> U[Generate Receipt]
    U --> V[Update Accounting]
    V --> W[Customer Notification]
```

**Module Scope**: Complete within Business Services Module
**Team**: Business Services Team
**Dependencies**: Payment Processing Module (for payment collection), Notification Module (for communications)

---

### **Agent Network Module Journeys**

#### **Cash-In Service Journey**
```mermaid
graph TD
    A[Customer Visits Agent] --> B[Agent Login]
    B --> C[Select Cash-In Service]
    C --> D[Enter Customer Phone]
    D --> E[Verify Customer Identity]
    E --> F[Enter Cash Amount]
    F --> G[Select Destination Account]
    G --> H[Calculate Fees]
    H --> I[Confirm Transaction Details]
    I --> J[Customer Approval]
    J --> K[Collect Cash]
    K --> L[Process Transaction]
    L --> M[Transaction Success]
    M --> N[Generate Receipt]
    N --> O[Customer Receives Funds]
    O --> P[Agent Commission Recorded]
    P --> Q[Transaction Complete]
```

**Module Scope**: Complete within Agent Network Module
**Team**: Agent Network Team
**Dependencies**: Payment Processing Module (for fund transfer), User Management Module (for customer verification)

---

### **Cards Module Journeys**

#### **Virtual Card Creation Journey**
```mermaid
graph TD
    A[Access Cards Section] --> B[Create Virtual Card]
    B --> C[Select Card Type]
    C --> D[Card Purpose Selection]
    D --> E[Set Card Limits]
    E --> F[Choose Card Design]
    F --> G[Link Funding Source]
    G --> H[Set Security Controls]
    H --> I[Review Card Details]
    I --> J[Create Card]
    J --> K[Card Generated]
    K --> L[Card Details Display]
    L --> M[Add to Digital Wallet]
    M --> N[Card Activation]
    N --> O[Card Ready for Use]
    
    O --> P[First Transaction]
    P --> Q[Transaction Authorization]
    Q --> R[Transaction Processing]
    R --> S[Transaction Success]
    S --> T[Transaction Notification]
```

**Module Scope**: Complete within Cards Module
**Team**: Cards Team
**Dependencies**: Account Management Module (for funding source), Payment Processing Module (for transactions)

---

### **Loans Module Journeys**

#### **Loan Application Journey**
```mermaid
graph TD
    A[Access Loan Services] --> B[Browse Loan Options]
    B --> C[Select Loan Type]
    C --> D[Loan Calculator]
    D --> E[Check Eligibility]
    E --> F[Start Application]
    F --> G[Personal Information]
    G --> H[Financial Information]
    H --> I[Employment Details]
    I --> J[Upload Documents]
    J --> K[Review Application]
    K --> L[Submit Application]
    L --> M[Application Received]
    
    M --> N[Credit Assessment]
    N --> O[Document Verification]
    O --> P[Risk Evaluation]
    P --> Q{Approval Decision}
    
    Q -->|Approved| R[Loan Approved]
    Q -->|Rejected| S[Loan Rejected]
    Q -->|More Info| T[Additional Information Required]
    
    R --> U[Loan Terms Presentation]
    U --> V[Accept Loan Terms]
    V --> W[Loan Agreement Signing]
    W --> X[Loan Disbursement]
    X --> Y[Funds Transferred]
    Y --> Z[Repayment Schedule Setup]
```

**Module Scope**: Complete within Loans Module
**Team**: Loans Team
**Dependencies**: User Management Module (for identity verification), Payment Processing Module (for disbursement)

---

## 🔗 **Cross-Cutting Journeys**

### **Authentication Journey (Spans All Modules)**
```mermaid
graph TD
    A[User Access Request] --> B{Authentication Status}

    B -->|Not Authenticated| C[Login Screen]
    B -->|Authenticated| D[Session Validation]

    C --> E[Enter Phone Number]
    E --> F[Enter PIN]
    F --> G{PIN Validation}

    G -->|Valid| H[Biometric Prompt]
    G -->|Invalid| I[PIN Error]

    I --> J{Retry Count}
    J -->|< 3 Attempts| F
    J -->|>= 3 Attempts| K[Account Locked]

    H --> L{Biometric Available}
    L -->|Yes| M[Biometric Verification]
    L -->|No| N[Skip Biometric]

    M --> O{Biometric Valid}
    O -->|Valid| P[Create Session]
    O -->|Invalid| Q[Fallback to PIN]

    N --> P
    Q --> F
    P --> R[Session Created]
    R --> S[Access Granted]

    D --> T{Session Valid}
    T -->|Valid| S
    T -->|Expired| U[Session Refresh]
    U --> V{Refresh Successful}
    V -->|Yes| S
    V -->|No| C
```

**Cross-Module Impact**: All modules require authentication
**Dependencies**: User Management Module (for user data), Notification Module (for security alerts)

### **Notification Journey (Spans All Modules)**
```mermaid
graph TD
    A[Module Event Trigger] --> B[Notification Service]
    B --> C[User Preferences Check]
    C --> D[Notification Template Selection]
    D --> E[Personalization]
    E --> F[Channel Selection]

    F --> G{Notification Channels}
    G -->|Push| H[Push Notification]
    G -->|SMS| I[SMS Notification]
    G -->|Email| J[Email Notification]
    G -->|In-App| K[In-App Message]

    H --> L[Delivery Attempt]
    I --> L
    J --> L
    K --> L

    L --> M{Delivery Status}
    M -->|Success| N[Mark Delivered]
    M -->|Failed| O[Retry Logic]

    O --> P{Retry Count}
    P -->|< Max Retries| Q[Schedule Retry]
    P -->|>= Max Retries| R[Mark Failed]

    Q --> L
    N --> S[Update Delivery Log]
    R --> S
    S --> T[Notification Complete]
```

**Cross-Module Impact**: All modules generate notifications
**Module Integration**: Each module publishes events to Notification Module

---

## 📊 **Module Independence Matrix**

### **Development Independence**
```yaml
Module_Independence_Verification:
  user_management_module:
    independent_development: "✅ Complete user lifecycle within module"
    team_autonomy: "✅ 6-person dedicated team"
    deployment_independence: "✅ Can deploy without other modules"
    scaling_independence: "✅ Scales based on user growth"
    technology_choice: "✅ Team chooses appropriate tech stack"

  payment_processing_module:
    independent_development: "✅ Complete payment functionality within module"
    team_autonomy: "✅ 8-person dedicated team"
    deployment_independence: "✅ Can deploy without other modules"
    scaling_independence: "✅ Scales based on transaction volume"
    technology_choice: "✅ Team chooses appropriate tech stack"

  group_savings_module:
    independent_development: "✅ Complete savings functionality within module"
    team_autonomy: "✅ 6-person dedicated team"
    deployment_independence: "✅ Can deploy without other modules"
    scaling_independence: "✅ Scales based on group participation"
    technology_choice: "✅ Team chooses appropriate tech stack"

  business_services_module:
    independent_development: "✅ Complete business functionality within module"
    team_autonomy: "✅ 7-person dedicated team"
    deployment_independence: "✅ Can deploy without other modules"
    scaling_independence: "✅ Scales based on business adoption"
    technology_choice: "✅ Team chooses appropriate tech stack"

  cards_module:
    independent_development: "✅ Complete card functionality within module"
    team_autonomy: "✅ 6-person dedicated team"
    deployment_independence: "✅ Can deploy without other modules"
    scaling_independence: "✅ Scales based on card usage"
    technology_choice: "✅ Team chooses appropriate tech stack"

  loans_module:
    independent_development: "✅ Complete loan functionality within module"
    team_autonomy: "✅ 7-person dedicated team"
    deployment_independence: "✅ Can deploy without other modules"
    scaling_independence: "✅ Scales based on loan volume"
    technology_choice: "✅ Team chooses appropriate tech stack"
```

### **User Journey Independence**
```yaml
Journey_Independence_Verification:
  individual_users:
    user_management: "✅ Complete registration and profile management"
    account_management: "✅ Complete account linking and management"
    payment_processing: "✅ Complete P2P and bill payment flows"
    group_savings: "✅ Complete group participation flows"
    cards: "✅ Complete card creation and management"
    loans: "✅ Complete loan application and management"

  business_users:
    user_management: "✅ Complete business registration and team setup"
    business_services: "✅ Complete business operations and management"
    payment_processing: "✅ Complete bulk payment and reconciliation"
    cards: "✅ Complete business card management"
    loans: "✅ Complete business loan processes"

  agent_users:
    user_management: "✅ Complete agent registration and verification"
    agent_network: "✅ Complete cash service operations"
    payment_processing: "✅ Complete agent-facilitated transactions"

  admin_users:
    admin_portal: "✅ Complete platform administration"
    compliance: "✅ Complete regulatory and compliance management"
    analytics: "✅ Complete business intelligence and reporting"
```

**This modular journey structure ensures each major feature can be developed independently while maintaining seamless user experiences across all user types.** 🗺️
