import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Contact {
  id: string
  name: string
  phone: string
  email?: string
  walletType?: string
  bankName?: string
  accountNumber?: string
  avatar?: string
  isFavorite: boolean
  lastTransactionDate?: Date
  totalTransactions: number
  tags: string[]
  notes?: string
  createdAt: Date
  updatedAt: Date
}

export interface RecentRecipient {
  id: string
  name: string
  phone?: string
  accountNumber?: string
  walletType?: string
  bankName?: string
  amount: number
  currency: 'USD' | 'ZWL'
  date: Date
  status: 'completed' | 'pending' | 'failed'
}

interface ContactState {
  contacts: Contact[]
  recentRecipients: RecentRecipient[]
  searchQuery: string
  selectedTags: string[]
  
  // Contact management
  addContact: (contact: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateContact: (id: string, updates: Partial<Contact>) => void
  deleteContact: (id: string) => void
  toggleFavorite: (id: string) => void
  
  // Recent recipients
  addRecentRecipient: (recipient: Omit<RecentRecipient, 'id'>) => void
  clearRecentRecipients: () => void
  
  // Search and filter
  setSearchQuery: (query: string) => void
  setSelectedTags: (tags: string[]) => void
  getFilteredContacts: () => Contact[]
  
  // Utility functions
  getContactByPhone: (phone: string) => Contact | undefined
  getContactsByTag: (tag: string) => Contact[]
  getAllTags: () => string[]
}

export const useContactStore = create<ContactState>()(
  persist(
    (set, get) => ({
      contacts: [
        {
          id: '1',
          name: 'John Doe',
          phone: '+************',
          email: '<EMAIL>',
          walletType: 'EcoCash',
          isFavorite: true,
          lastTransactionDate: new Date('2024-01-15'),
          totalTransactions: 15,
          tags: ['family', 'frequent'],
          notes: 'Brother - monthly allowance',
          createdAt: new Date('2023-06-01'),
          updatedAt: new Date('2024-01-15')
        },
        {
          id: '2',
          name: 'Jane Smith',
          phone: '+************',
          email: '<EMAIL>',
          walletType: 'OneMoney',
          isFavorite: false,
          lastTransactionDate: new Date('2024-01-10'),
          totalTransactions: 8,
          tags: ['work', 'colleague'],
          notes: 'Office colleague',
          createdAt: new Date('2023-08-15'),
          updatedAt: new Date('2024-01-10')
        },
        {
          id: '3',
          name: 'Mike Johnson',
          phone: '+************',
          bankName: 'ZB Bank',
          accountNumber: '***********',
          isFavorite: true,
          lastTransactionDate: new Date('2024-01-12'),
          totalTransactions: 22,
          tags: ['business', 'supplier'],
          notes: 'Office supplies vendor',
          createdAt: new Date('2023-05-20'),
          updatedAt: new Date('2024-01-12')
        }
      ],
      recentRecipients: [
        {
          id: '1',
          name: 'John Doe',
          phone: '+************',
          walletType: 'EcoCash',
          amount: 500,
          currency: 'USD',
          date: new Date('2024-01-15T10:30:00'),
          status: 'completed'
        },
        {
          id: '2',
          name: 'Sarah Wilson',
          phone: '+************',
          walletType: 'OneMoney',
          amount: 250,
          currency: 'USD',
          date: new Date('2024-01-14T15:45:00'),
          status: 'completed'
        },
        {
          id: '3',
          name: 'Mike Johnson',
          accountNumber: '***********',
          bankName: 'ZB Bank',
          amount: 1000,
          currency: 'USD',
          date: new Date('2024-01-12T09:15:00'),
          status: 'completed'
        }
      ],
      searchQuery: '',
      selectedTags: [],

      addContact: (contactData) => {
        const newContact: Contact = {
          ...contactData,
          id: Date.now().toString(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
        set((state) => ({
          contacts: [...state.contacts, newContact]
        }))
      },

      updateContact: (id, updates) => {
        set((state) => ({
          contacts: state.contacts.map((contact) =>
            contact.id === id
              ? { ...contact, ...updates, updatedAt: new Date() }
              : contact
          )
        }))
      },

      deleteContact: (id) => {
        set((state) => ({
          contacts: state.contacts.filter((contact) => contact.id !== id)
        }))
      },

      toggleFavorite: (id) => {
        set((state) => ({
          contacts: state.contacts.map((contact) =>
            contact.id === id
              ? { ...contact, isFavorite: !contact.isFavorite, updatedAt: new Date() }
              : contact
          )
        }))
      },

      addRecentRecipient: (recipientData) => {
        const newRecipient: RecentRecipient = {
          ...recipientData,
          id: Date.now().toString()
        }
        set((state) => ({
          recentRecipients: [newRecipient, ...state.recentRecipients.slice(0, 9)] // Keep only 10 recent
        }))
      },

      clearRecentRecipients: () => {
        set({ recentRecipients: [] })
      },

      setSearchQuery: (query) => {
        set({ searchQuery: query })
      },

      setSelectedTags: (tags) => {
        set({ selectedTags: tags })
      },

      getFilteredContacts: () => {
        const { contacts, searchQuery, selectedTags } = get()
        
        return contacts.filter((contact) => {
          const matchesSearch = !searchQuery || 
            contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            contact.phone.includes(searchQuery) ||
            contact.email?.toLowerCase().includes(searchQuery.toLowerCase())
          
          const matchesTags = selectedTags.length === 0 ||
            selectedTags.some(tag => contact.tags.includes(tag))
          
          return matchesSearch && matchesTags
        })
      },

      getContactByPhone: (phone) => {
        return get().contacts.find((contact) => contact.phone === phone)
      },

      getContactsByTag: (tag) => {
        return get().contacts.filter((contact) => contact.tags.includes(tag))
      },

      getAllTags: () => {
        const { contacts } = get()
        const allTags = contacts.flatMap((contact) => contact.tags)
        return [...new Set(allTags)].sort()
      }
    }),
    {
      name: 'contact-storage',
    }
  )
)
