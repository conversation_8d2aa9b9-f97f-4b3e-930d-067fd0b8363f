"use client";

import { useState } from 'react'
import { DashboardShell } from '@/components/dashboard-shell'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Calendar,
  PieChart,
  BarChart3,
  ArrowUpIcon,
  ArrowDownIcon,
  Wallet,
  Receipt,
  Users,
  CreditCard
} from 'lucide-react'

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('30d')

  // Mock analytics data
  const monthlyData = {
    income: 2500,
    expenses: 1850,
    savings: 650,
    savingsRate: 26,
    previousMonth: {
      income: 2300,
      expenses: 1920,
      savings: 380
    }
  }

  const expenseCategories = [
    { name: 'Bills & Utilities', amount: 650, percentage: 35, color: 'bg-blue-500' },
    { name: 'Food & Groceries', amount: 480, percentage: 26, color: 'bg-green-500' },
    { name: 'Transportation', amount: 280, percentage: 15, color: 'bg-yellow-500' },
    { name: 'Entertainment', amount: 220, percentage: 12, color: 'bg-purple-500' },
    { name: 'Shopping', amount: 150, percentage: 8, color: 'bg-pink-500' },
    { name: 'Other', amount: 70, percentage: 4, color: 'bg-gray-500' }
  ]

  const savingsGoals = [
    {
      id: 1,
      name: 'Emergency Fund',
      target: 10000,
      current: 6500,
      deadline: '2024-12-31',
      priority: 'high'
    },
    {
      id: 2,
      name: 'Vacation Fund',
      target: 3000,
      current: 1200,
      deadline: '2024-06-30',
      priority: 'medium'
    },
    {
      id: 3,
      name: 'New Car',
      target: 15000,
      current: 4500,
      deadline: '2025-03-31',
      priority: 'low'
    }
  ]

  const recentTransactions = [
    { id: 1, type: 'expense', category: 'Bills', description: 'ZESA Electricity Bill', amount: 120, date: 'Today' },
    { id: 2, type: 'income', category: 'Salary', description: 'Monthly Salary', amount: 2500, date: 'Jan 1' },
    { id: 3, type: 'expense', category: 'Food', description: 'Grocery Shopping', amount: 85, date: 'Yesterday' },
    { id: 4, type: 'expense', category: 'Transport', description: 'Fuel', amount: 60, date: 'Jan 20' }
  ]

  const incomeChange = ((monthlyData.income - monthlyData.previousMonth.income) / monthlyData.previousMonth.income) * 100
  const expenseChange = ((monthlyData.expenses - monthlyData.previousMonth.expenses) / monthlyData.previousMonth.expenses) * 100
  const savingsChange = ((monthlyData.savings - monthlyData.previousMonth.savings) / monthlyData.previousMonth.savings) * 100

  return (
    <DashboardShell>
      <div className="container px-4 sm:px-6 space-y-6 sm:space-y-8 pb-20 md:pb-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Financial Analytics</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Track your spending, savings, and financial goals
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 3 months</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Income</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {monthlyData.income.toLocaleString()}</div>
              <div className="flex items-center text-xs text-green-600">
                <ArrowUpIcon className="h-3 w-3 mr-1" />
                +{incomeChange.toFixed(1)}% from last month
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {monthlyData.expenses.toLocaleString()}</div>
              <div className="flex items-center text-xs text-red-600">
                <ArrowDownIcon className="h-3 w-3 mr-1" />
                {expenseChange.toFixed(1)}% from last month
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Savings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {monthlyData.savings.toLocaleString()}</div>
              <div className="flex items-center text-xs text-green-600">
                <ArrowUpIcon className="h-3 w-3 mr-1" />
                +{savingsChange.toFixed(1)}% from last month
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Savings Rate</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{monthlyData.savingsRate}%</div>
              <p className="text-xs text-muted-foreground">Of total income</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="spending" className="space-y-4">
          <TabsList>
            <TabsTrigger value="spending">Spending Analysis</TabsTrigger>
            <TabsTrigger value="goals">Savings Goals</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <TabsContent value="spending" className="space-y-4">
            {/* Expense Categories */}
            <Card>
              <CardHeader>
                <CardTitle>Expense Breakdown</CardTitle>
                <CardDescription>Where your money goes this month</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {expenseCategories.map((category, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">{category.name}</span>
                      <span>USD {category.amount} ({category.percentage}%)</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${category.color}`}></div>
                      <Progress value={category.percentage} className="flex-1" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Your latest financial activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentTransactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full ${
                          transaction.type === 'income' 
                            ? 'bg-green-100 text-green-600' 
                            : 'bg-red-100 text-red-600'
                        }`}>
                          {transaction.type === 'income' ? (
                            <ArrowDownIcon className="h-4 w-4" />
                          ) : (
                            <ArrowUpIcon className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-sm text-muted-foreground">{transaction.category} • {transaction.date}</p>
                        </div>
                      </div>
                      <div className={`font-medium ${
                        transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'income' ? '+' : '-'}USD {transaction.amount}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="goals" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Savings Goals</CardTitle>
                <CardDescription>Track your progress towards financial goals</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {savingsGoals.map((goal) => (
                  <div key={goal.id} className="space-y-3">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-semibold">{goal.name}</h3>
                        <p className="text-sm text-muted-foreground">Target: {goal.deadline}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">USD {goal.current.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">of USD {goal.target.toLocaleString()}</p>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <Progress value={(goal.current / goal.target) * 100} />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{Math.round((goal.current / goal.target) * 100)}% complete</span>
                        <Badge variant={
                          goal.priority === 'high' ? 'destructive' : 
                          goal.priority === 'medium' ? 'default' : 'secondary'
                        }>
                          {goal.priority} priority
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Financial Trends</CardTitle>
                <CardDescription>Your financial patterns over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4" />
                  <p>Detailed trend charts coming soon</p>
                  <p className="text-sm">Track your income, expenses, and savings over time</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  )
}
