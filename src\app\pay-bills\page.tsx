"use client";

import { useState } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  BoltIcon,
  WifiIcon,
  PhoneIcon,
  TvIcon,
  DropletIcon,
  GlobeIcon,
  ChevronDownIcon,
  CheckIcon,
  Phone,
  Zap,
  Music2,
  Radio,
  Store,
  Receipt,
  Search,
  Plus,
  X
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useWalletStore } from "@/lib/stores/wallet-store";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { LucideIcon } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface Bill {
  id: string
  type: string
  amount: number
  currency: string
  dueDate: string
  status: 'pending' | 'paid' | 'overdue'
  reference: string
  provider: string
  description: string
}

interface BillPayment {
  billId: string
  amount: number
  currency: string
  paymentMethod: string
  timestamp: string
  status: 'success' | 'failed' | 'pending'
  reference: string
}

interface Biller {
  id: number
  name: string
  category: 'airtime' | 'utilities' | 'entertainment' | 'telecoms' | 'merchants' | 'billers'
  icon: LucideIcon
  merchantCode?: string
  billerCode?: string
}

interface AddBillerFormData {
  name: string
  category: Biller['category']
  icon: LucideIcon
  merchantCode?: string
  billerCode?: string
}

interface BillCategory {
  id: string;
  label: string;
  icon: LucideIcon;
  bills: Biller[];
}

// Mock bill providers
const initialBillProviders: Biller[] = [
  {
    id: 1,
    name: "Add New Biller",
    category: "merchants",
    icon: Plus,
    merchantCode: "NEW"
  },
  {
    id: 2,
    name: "Add New Biller",
    category: "billers",
    icon: Plus,
    billerCode: "NEW"
  },
  {
    id: 3,
    name: "ZESA",
    category: "utilities",
    icon: Zap,
    merchantCode: "ZESA"
  },
  {
    id: 4,
    name: "ZINWA",
    category: "utilities",
    icon: DropletIcon,
    billerCode: "ZINWA"
  },
  {
    id: 5,
    name: "DSTV",
    category: "entertainment",
    icon: TvIcon,
    merchantCode: "DSTV"
  },
  {
    id: 6,
    name: "ZOL",
    category: "telecoms",
    icon: WifiIcon,
    merchantCode: "ZOL"
  },
  {
    id: 7,
    name: "Liquid",
    category: "telecoms",
    icon: WifiIcon,
    merchantCode: "LIQUID"
  },
  {
    id: 8,
    name: "Econet",
    category: "telecoms",
    icon: PhoneIcon,
    merchantCode: "ECONET"
  },
  {
    id: 9,
    name: "NetOne",
    category: "telecoms",
    icon: PhoneIcon,
    merchantCode: "NETONE"
  },
  {
    id: 10,
    name: "Telecel",
    category: "telecoms",
    icon: PhoneIcon,
    merchantCode: "TELECEL"
  }
];

// Demo data - replace with actual data from your backend
const billCategories: BillCategory[] = [
  {
    id: "all",
    label: "All",
    icon: Receipt,
    bills: [
      { id: 1, name: "Econet Airtime", category: "airtime" as const, icon: Phone },
      { id: 2, name: "ZESA Prepaid", category: "utilities" as const, icon: Zap },
      { id: 3, name: "Netflix", category: "entertainment" as const, icon: Music2 },
      { id: 4, name: "TelOne", category: "telecoms" as const, icon: Radio },
      { id: 5, name: "OK Zimbabwe", category: "merchants" as const, icon: Store },
      { id: 6, name: "City of Harare", category: "billers" as const, icon: Receipt }
    ]
  },
  {
    id: "airtime",
    label: "Airtime",
    icon: Phone,
    bills: [
      { id: 1, name: "Econet Airtime", category: "airtime" as const, icon: Phone },
      { id: 2, name: "NetOne Airtime", category: "airtime" as const, icon: Phone },
      { id: 3, name: "Telecel Airtime", category: "airtime" as const, icon: Phone }
    ]
  },
  {
    id: "utilities",
    label: "Utilities",
    icon: Zap,
    bills: [
      { id: 1, name: "ZESA Prepaid", category: "utilities" as const, icon: Zap },
      { id: 2, name: "ZESA Postpaid", category: "utilities" as const, icon: Zap },
      { id: 3, name: "Water Bill", category: "utilities" as const, icon: Zap }
    ]
  },
  {
    id: "entertainment",
    label: "Entertainment",
    icon: Music2,
    bills: [
      { id: 1, name: "Netflix", category: "entertainment" as const, icon: Music2 },
      { id: 2, name: "DStv", category: "entertainment" as const, icon: Music2 },
      { id: 3, name: "Showmax", category: "entertainment" as const, icon: Music2 }
    ]
  },
  {
    id: "telecoms",
    label: "Telecoms",
    icon: Radio,
    bills: [
      { id: 1, name: "TelOne", category: "telecoms" as const, icon: Radio },
      { id: 2, name: "ZOL Fiber", category: "telecoms" as const, icon: Radio },
      { id: 3, name: "Econet Data", category: "telecoms" as const, icon: Radio }
    ]
  },
  {
    id: "merchants",
    label: "Merchants",
    icon: Store,
    bills: [
      { id: 1, name: "OK Zimbabwe", category: "merchants" as const, icon: Store },
      { id: 2, name: "Pick n Pay", category: "merchants" as const, icon: Store },
      { id: 3, name: "Food World", category: "merchants" as const, icon: Store }
    ]
  },
  {
    id: "billers",
    label: "Billers",
    icon: Receipt,
    bills: [
      { id: 1, name: "City of Harare", category: "billers" as const, icon: Receipt },
      { id: 2, name: "ZIMRA", category: "billers" as const, icon: Receipt },
      { id: 3, name: "School Fees", category: "billers" as const, icon: Receipt }
    ]
  }
]

// Update the saved merchants and billers data
const savedMerchants: Biller[] = [
  { id: 1, name: "OK Zimbabwe", merchantCode: "OKZ123", category: "merchants", icon: Store },
  { id: 2, name: "Pick n Pay", merchantCode: "PNP456", category: "merchants", icon: Store },
  { id: 3, name: "Food World", merchantCode: "FWD789", category: "merchants", icon: Store }
];

const savedBillers: Biller[] = [
  { id: 1, name: "City of Harare", billerCode: "COH123", category: "billers", icon: Receipt },
  { id: 2, name: "ZIMRA", billerCode: "ZIM456", category: "billers", icon: Receipt },
  { id: 3, name: "School Fees", billerCode: "SCH789", category: "billers", icon: Receipt }
];

interface PaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  biller: Biller;
}

function AirtimePaymentModal({ open, onOpenChange, biller }: PaymentModalProps) {
  const [buyType, setBuyType] = useState<"self" | "other">("self");
  const [phone, setPhone] = useState("");
  const [amount, setAmount] = useState("");
  const userPhone = "0771234567"; // This should come from user profile
  const { toast } = useToast();

  const handleSubmit = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success!",
        description: `Successfully purchased ${biller.name} airtime for ${buyType === "self" ? userPhone : phone}`,
      });

      // Reset form
      setBuyType("self");
      setPhone("");
      setAmount("");
      
      // Close modal
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process airtime purchase. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Buy {biller.name}</DialogTitle>
          <DialogDescription>Enter airtime purchase details</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Purchase Type</Label>
            <RadioGroup value={buyType} onValueChange={(value: "self" | "other") => setBuyType(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="self" id="self" />
                <Label htmlFor="self">Buy for myself ({userPhone})</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="other" id="other" />
                <Label htmlFor="other">Buy for someone else</Label>
              </div>
            </RadioGroup>
          </div>
          
          {buyType === "other" && (
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                placeholder="Enter phone number"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSubmit} disabled={!amount || (buyType === "other" && !phone)}>Buy Airtime</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function UtilityPaymentModal({ open, onOpenChange, biller }: PaymentModalProps) {
  const [accountNumber, setAccountNumber] = useState("");
  const [amount, setAmount] = useState("");
  const { toast } = useToast();

  const handleSubmit = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success!",
        description: `Successfully paid ${biller.name} bill for account ${accountNumber}`,
      });

      // Reset form
      setAccountNumber("");
      setAmount("");
      
      // Close modal
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process utility payment. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Pay {biller.name}</DialogTitle>
          <DialogDescription>Enter payment details</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="account">Account/Meter Number</Label>
            <Input
              id="account"
              placeholder="Enter account or meter number"
              value={accountNumber}
              onChange={(e) => setAccountNumber(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSubmit} disabled={!accountNumber || !amount}>Pay Now</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function MerchantPaymentModal({ open, onOpenChange, biller }: PaymentModalProps) {
  const [merchantCode, setMerchantCode] = useState(biller?.merchantCode || "");
  const [amount, setAmount] = useState("");
  const { toast } = useToast();

  const handleSubmit = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success!",
        description: `Successfully paid ${biller.name} with merchant code ${merchantCode}`,
      });

      // Reset form
      setMerchantCode(biller?.merchantCode || "");
      setAmount("");
      
      // Close modal
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process merchant payment. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Pay {biller.name}</DialogTitle>
          <DialogDescription>Enter merchant payment details</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="merchant">Merchant Code</Label>
            <Input
              id="merchant"
              placeholder="Enter merchant code"
              value={merchantCode}
              onChange={(e) => setMerchantCode(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSubmit} disabled={!merchantCode || !amount}>Pay Now</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function BillerPaymentModal({ open, onOpenChange, biller }: PaymentModalProps) {
  const [billerCode, setBillerCode] = useState(biller?.billerCode || "");
  const [amount, setAmount] = useState("");
  const { toast } = useToast();

  const handleSubmit = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success!",
        description: `Successfully paid ${biller.name} with biller code ${billerCode}`,
      });

      // Reset form
      setBillerCode(biller?.billerCode || "");
      setAmount("");
      
      // Close modal
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process biller payment. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Pay {biller.name}</DialogTitle>
          <DialogDescription>Enter biller payment details</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="biller">Biller Code</Label>
            <Input
              id="biller"
              placeholder="Enter biller code"
              value={billerCode}
              onChange={(e) => setBillerCode(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSubmit} disabled={!billerCode || !amount}>Pay Now</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function AddBillerForm({ onSubmit, onCancel }: { onSubmit: (data: AddBillerFormData) => void; onCancel: () => void }) {
  const [formData, setFormData] = useState<AddBillerFormData>({
    name: "",
    category: "utilities",
    icon: Plus,
    billerCode: ""
  });
  const [saveForLater, setSaveForLater] = useState(false);
  const [amount, setAmount] = useState("");
  const [billerCategory, setBillerCategory] = useState("ecocash");
  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      name: "",
      category: "utilities",
      icon: Plus,
      billerCode: ""
    });
    setSaveForLater(false);
    setAmount("");
    setBillerCategory("ecocash");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (saveForLater) {
        onSubmit(formData);
      }
      
      toast({
        title: "Success!",
        description: saveForLater 
          ? "Biller has been saved and payment processed successfully."
          : "Payment processed successfully.",
      });

      resetForm();
      onCancel(); // Close modal
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process request. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="billerCategory">Biller Category</Label>
        <Select
          value={billerCategory}
          onValueChange={setBillerCategory}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ecocash">Ecocash Billers</SelectItem>
            <SelectItem value="zbuniwallet">ZB UniWallet Billers</SelectItem>
            <SelectItem value="onemoney">OneMoney Billers</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="billerCode">Biller Code</Label>
        <Input
          id="billerCode"
          placeholder="Enter biller code"
          value={formData.billerCode}
          onChange={(e) => setFormData(prev => ({ ...prev, billerCode: e.target.value }))}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="amount">Amount</Label>
        <Input
          id="amount"
          type="number"
          placeholder="Enter amount"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          required
        />
      </div>

      <div className="flex items-center space-x-2 py-2">
        <input
          type="checkbox"
          id="saveBiller"
          checked={saveForLater}
          onChange={(e) => setSaveForLater(e.target.checked)}
          className="h-4 w-4 rounded border-gray-300"
        />
        <Label htmlFor="saveBiller">Save this biller for future use</Label>
      </div>

      {saveForLater && (
        <div className="space-y-2">
          <Label htmlFor="name">Biller Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required={saveForLater}
            placeholder="Enter biller name to save"
          />
        </div>
      )}

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          type="submit"
          disabled={!formData.billerCode || !amount || (saveForLater && !formData.name)}
        >
          {saveForLater ? "Save & Pay" : "Pay Now"}
        </Button>
      </div>
    </form>
  );
}

function AddMerchantForm({ onSubmit, onCancel }: { onSubmit: (data: AddBillerFormData) => void; onCancel: () => void }) {
  const [formData, setFormData] = useState<AddBillerFormData>({
    name: "",
    category: "merchants",
    icon: Plus,
    merchantCode: ""
  });
  const [saveForLater, setSaveForLater] = useState(false);
  const [amount, setAmount] = useState("");
  const [merchantCategory, setMerchantCategory] = useState("ecocash");
  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      name: "",
      category: "merchants",
      icon: Plus,
      merchantCode: ""
    });
    setSaveForLater(false);
    setAmount("");
    setMerchantCategory("ecocash");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (saveForLater) {
        onSubmit(formData);
      }
      
      toast({
        title: "Success!",
        description: saveForLater 
          ? "Merchant has been saved and payment processed successfully."
          : "Payment processed successfully.",
      });

      resetForm();
      onCancel(); // Close modal
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process request. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="merchantCategory">Merchant Category</Label>
        <Select
          value={merchantCategory}
          onValueChange={setMerchantCategory}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ecocash">Ecocash Merchants</SelectItem>
            <SelectItem value="zbuniwallet">ZB UniWallet Merchants</SelectItem>
            <SelectItem value="onemoney">OneMoney Merchants</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="merchantCode">Merchant Code</Label>
        <Input
          id="merchantCode"
          placeholder="Enter merchant code"
          value={formData.merchantCode}
          onChange={(e) => setFormData(prev => ({ ...prev, merchantCode: e.target.value }))}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="amount">Amount</Label>
        <Input
          id="amount"
          type="number"
          placeholder="Enter amount"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          required
        />
      </div>

      <div className="flex items-center space-x-2 py-2">
        <input
          type="checkbox"
          id="saveMerchant"
          checked={saveForLater}
          onChange={(e) => setSaveForLater(e.target.checked)}
          className="h-4 w-4 rounded border-gray-300"
        />
        <Label htmlFor="saveMerchant">Save this merchant for future use</Label>
      </div>

      {saveForLater && (
        <div className="space-y-2">
          <Label htmlFor="name">Merchant Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required={saveForLater}
            placeholder="Enter merchant name to save"
          />
        </div>
      )}

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          type="submit"
          disabled={!formData.merchantCode || !amount || (saveForLater && !formData.name)}
        >
          {saveForLater ? "Save & Pay" : "Pay Now"}
        </Button>
      </div>
    </form>
  );
}

function AddBillerModal({ open, onOpenChange }: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Pay New Biller</DialogTitle>
          <DialogDescription>Enter payment details for the new biller</DialogDescription>
        </DialogHeader>
        <AddBillerForm onSubmit={() => {}} onCancel={() => onOpenChange(false)} />
      </DialogContent>
    </Dialog>
  );
}

function AddMerchantModal({ open, onOpenChange }: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Pay New Merchant</DialogTitle>
          <DialogDescription>Enter payment details for the new merchant</DialogDescription>
        </DialogHeader>
        <AddMerchantForm onSubmit={() => {}} onCancel={() => onOpenChange(false)} />
      </DialogContent>
    </Dialog>
  );
}

export default function PayBillsPage() {
  const {
    wallets,
    selectedWallet,
    setSelectedWallet,
    getWalletBalance,
    getWalletById
  } = useWalletStore();
  
  const [walletOpen, setWalletOpen] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState<"ZWL" | "USD">("ZWL");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showAddBillerModal, setShowAddBillerModal] = useState(false);
  const [showAddMerchantModal, setShowAddMerchantModal] = useState(false);
  const [selectedBiller, setSelectedBiller] = useState<Biller | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<BillPayment[]>([]);
  const [savedBillProviders, setSavedBillProviders] = useState<Biller[]>(initialBillProviders);

  const selectedWalletData = selectedWallet ? getWalletById(selectedWallet) : null;

  const filteredBills = billCategories.find(cat => cat.id === selectedCategory)?.bills.filter(bill =>
    bill.name.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const handlePayNow = (biller: Biller) => {
    setSelectedBiller(biller);
    setShowPaymentModal(true);
  };

  const closePaymentModal = () => {
    setSelectedBiller(null);
    setShowPaymentModal(false);
  };

  const handleAddBiller = (data: AddBillerFormData) => {
    const newBiller: Biller = {
      id: savedBillProviders.length + 1,
      ...data
    };
    setSavedBillProviders([...savedBillProviders, newBiller]);
    setShowAddBillerModal(false);
  };

  const handleAddMerchant = (data: AddBillerFormData) => {
    const newBiller: Biller = {
      id: savedBillProviders.length + 1,
      ...data
    };
    setSavedBillProviders([...savedBillProviders, newBiller]);
    setShowAddMerchantModal(false);
  };

  return (
    <DashboardShell>
      <Card>
        <CardHeader>
          <CardTitle>Pay Bills</CardTitle>
          <CardDescription>Pay your bills and utilities quickly and securely</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6 px-4 md:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Pay Bills</h1>
                <p className="text-muted-foreground">Pay your bills and services</p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 sm:justify-end">
              <Button
                variant="outline"
                className="flex-1 sm:flex-initial"
                onClick={() => setShowAddMerchantModal(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                New Merchant
              </Button>
              <Button
                variant="outline"
                className="flex-1 sm:flex-initial"
                onClick={() => setShowAddBillerModal(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                New Biller
              </Button>
            </div>

            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search billers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Categories - Dropdown for Mobile, Tabs for Desktop */}
            <div className="block md:hidden">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full">
                  <SelectValue>
                    {billCategories.find(cat => cat.id === selectedCategory)?.label || "All"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {billCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      <div className="flex items-center gap-2">
                        <category.icon className="h-4 w-4" />
                        <span>{category.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="hidden md:block">
              <Tabs defaultValue="all" value={selectedCategory} onValueChange={setSelectedCategory}>
                <TabsList className="grid grid-cols-6 gap-4">
                  {billCategories.map((category) => (
                    <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
                      <category.icon className="h-4 w-4" />
                      <span>{category.label}</span>
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>

            {/* Bills Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {billCategories
                .find((cat) => cat.id === selectedCategory)
                ?.bills.filter((bill) =>
                  bill.name.toLowerCase().includes(searchQuery.toLowerCase())
                )
                .map((bill) => (
                  <Card key={bill.id} className="flex flex-col">
                    <CardHeader className="flex flex-row items-center gap-4">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <bill.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{bill.name}</CardTitle>
                        <CardDescription>Pay your {bill.name.toLowerCase()} bill</CardDescription>
                      </div>
                    </CardHeader>
                    <CardFooter className="mt-auto pt-6">
                      <Button
                        className="w-full"
                        onClick={() => handlePayNow(bill)}
                      >
                        Pay Now
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
            </div>

            {/* Modals */}
            {selectedBiller && (
              <>
                {selectedBiller.category === "airtime" && (
                  <AirtimePaymentModal
                    open={showPaymentModal}
                    onOpenChange={setShowPaymentModal}
                    biller={selectedBiller}
                  />
                )}
                {(selectedBiller.category === "utilities" || selectedBiller.category === "telecoms") && (
                  <UtilityPaymentModal
                    open={showPaymentModal}
                    onOpenChange={setShowPaymentModal}
                    biller={selectedBiller}
                  />
                )}
                {selectedBiller.category === "merchants" && (
                  <MerchantPaymentModal
                    open={showPaymentModal}
                    onOpenChange={setShowPaymentModal}
                    biller={selectedBiller}
                  />
                )}
                {selectedBiller.category === "billers" && (
                  <BillerPaymentModal
                    open={showPaymentModal}
                    onOpenChange={setShowPaymentModal}
                    biller={selectedBiller}
                  />
                )}
              </>
            )}

            <AddMerchantModal
              open={showAddMerchantModal}
              onOpenChange={setShowAddMerchantModal}
            />

            <AddBillerModal
              open={showAddBillerModal}
              onOpenChange={setShowAddBillerModal}
            />
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  );
}
