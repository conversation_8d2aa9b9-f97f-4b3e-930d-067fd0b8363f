"use client";

import { useState, useEffect, Suspense } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { ArrowRightIcon, CheckIcon, ChevronDownIcon } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSearchParams } from "next/navigation";
import { useWalletStore } from "@/lib/stores/wallet-store";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

// Wallet types available for sending money to
const walletTypes = [
  { id: "ecocash", name: "EcoCash", logo: "/images/wallets/ecocash.png" },
  { id: "onemoney", name: "OneMoney", logo: "/images/wallets/onemoney.png" },
  { id: "innbucks", name: "InnBucks", logo: "/images/wallets/innbucks.png" },
  { id: "omari", name: "O'mari", logo: "/images/wallets/omari-logo.png" },
  { id: "zbuniwallet", name: "ZB UniWallet", logo: "/images/zb-logo.png" }
];

// Banks available for bank transfers
const banks = [
  { id: "zb", name: "ZB Bank", logo: "/images/zb-logo.png" },
  { id: "cbz", name: "CBZ Bank", logo: "/images/wallets/ecocash.png" },
  { id: "stanbic", name: "Stanbic Bank", logo: "/images/wallets/innbucks.png" },
  { id: "fnb", name: "FNB Zimbabwe", logo: "/images/wallets/onemoney.png" },
  { id: "nedbank", name: "Nedbank Zimbabwe", logo: "/images/wallets/omari-logo.png" },
  { id: "ecobank", name: "Ecobank Zimbabwe", logo: "/images/wallets/ecocash.png" },
  { id: "stewardbank", name: "Steward Bank", logo: "/images/wallets/omari-logo.png" },
];

type RecipientType = "wallet" | "bank";

function SendMoneyContent() {
  const searchParams = useSearchParams();
  const {
    wallets,
    selectedWallet,
    setSelectedWallet,
    getWalletBalance,
    getWalletById
  } = useWalletStore();
  const { toast } = useToast();
  const router = useRouter();
  
  const [walletOpen, setWalletOpen] = useState(false);
  const [recipientType, setRecipientType] = useState<RecipientType>("wallet");
  const [selectedCurrency, setSelectedCurrency] = useState<"ZWL" | "USD">("ZWL");

  // Wallet recipient states
  const [selectedWalletType, setSelectedWalletType] = useState<string | null>(null);
  const [walletTypeOpen, setWalletTypeOpen] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState<string>("");

  // Bank recipient states
  const [selectedBank, setSelectedBank] = useState<string | null>(null);
  const [bankOpen, setBankOpen] = useState(false);
  const [accountNumber, setAccountNumber] = useState<string>("");
  const [accountName, setAccountName] = useState<string>("");
  const [branchCode, setBranchCode] = useState<string>("");

  const [amount, setAmount] = useState<string>("");
  const [reason, setReason] = useState<string>("");

  const selectedWalletData = selectedWallet ? getWalletById(selectedWallet) : null;

  const selectedWalletTypeData = selectedWalletType
    ? walletTypes.find(w => w.id === selectedWalletType)
    : null;

  const selectedBankData = selectedBank
    ? banks.find(b => b.id === selectedBank)
    : null;

  const isValidAmount = amount &&
    Number(amount) > 0 &&
    selectedWallet &&
    Number(amount) <= getWalletBalance(selectedWallet, selectedCurrency);

  const isWalletFormValid =
    selectedWallet &&
    selectedWalletType &&
    phoneNumber &&
    isValidAmount;

  const isBankFormValid =
    selectedWallet &&
    selectedBank &&
    accountNumber &&
    accountName &&
    isValidAmount;

  const isFormValid = recipientType === "wallet"
    ? isWalletFormValid
    : isBankFormValid;

  // Effect to set the initial wallet based on URL parameter
  useEffect(() => {
    const walletParam = searchParams.get('wallet');
    if (walletParam && wallets.some(w => w.id === walletParam && w.isConnected)) {
      setSelectedWallet(walletParam);
    }
  }, [searchParams, wallets, setSelectedWallet]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Clear form
      setSelectedWallet(null);
      setSelectedWalletType(null);
      setPhoneNumber("");
      setSelectedBank(null);
      setAccountNumber("");
      setAccountName("");
      setBranchCode("");
      setAmount("");
      setReason("");

      toast({
        title: "Success!",
        description: "Money sent successfully.",
      });

      // Navigate back or to confirmation page
      router.push('/dashboard');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send money. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <DashboardShell>
      <Card>
        <CardHeader>
          <CardTitle>Send Money</CardTitle>
          <CardDescription>Transfer funds to other accounts quickly and securely</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mx-auto max-w-md space-y-4 sm:space-y-6 px-4 sm:px-0">
            <Card>
              <CardHeader className="space-y-2 sm:space-y-3">
                <CardTitle className="text-lg sm:text-xl">Select Source Wallet</CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  Choose the wallet you want to send money from
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <DropdownMenu open={walletOpen} onOpenChange={setWalletOpen}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className={`w-full justify-between ${!selectedWallet ? 'text-muted-foreground' : ''}`}
                      >
                        {selectedWalletData ? (
                          <div className="flex items-center gap-2">
                            <div className="relative h-5 w-5">
                              <Image
                                src={selectedWalletData.logo}
                                alt={selectedWalletData.name}
                                fill
                                className="object-contain"
                              />
                            </div>
                            <span className="text-sm sm:text-base">{selectedWalletData.name}</span>
                          </div>
                        ) : (
                          <span className="text-sm sm:text-base">Select a wallet</span>
                        )}
                        <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full min-w-[300px]" align="start">
                      {wallets.filter(wallet => wallet.isConnected).map((wallet) => (
                        <DropdownMenuItem
                          key={wallet.id}
                          className="flex cursor-pointer items-center justify-between p-3"
                          onClick={() => {
                            setSelectedWallet(wallet.id);
                            setWalletOpen(false);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <div className="relative h-6 w-6 overflow-hidden rounded-md">
                              <Image
                                src={wallet.logo}
                                alt={wallet.name}
                                fill
                                className="object-contain"
                              />
                            </div>
                            <div>
                              <div className="font-medium text-sm sm:text-base">{wallet.name}</div>
                              <div className="flex gap-2 text-xs text-muted-foreground">
                                <span>ZWL {getWalletBalance(wallet.id, "ZWL").toLocaleString()}</span>
                                <span>•</span>
                                <span>USD {getWalletBalance(wallet.id, "USD").toLocaleString()}</span>
                              </div>
                            </div>
                          </div>
                          {selectedWallet === wallet.id && (
                            <CheckIcon className="h-4 w-4 text-primary" />
                          )}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {selectedWallet && selectedWalletData && (
                    <div className="space-y-4">
                      <div className="rounded-lg border bg-muted/30 p-3 sm:p-4">
                        <div className="text-xs sm:text-sm text-muted-foreground mb-2">Available Balance</div>
                        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-6">
                          <div>
                            <p className="text-lg sm:text-xl font-semibold text-foreground">
                              ZWL {getWalletBalance(selectedWallet, "ZWL").toLocaleString()}
                            </p>
                          </div>
                          <div className="hidden sm:block h-8 w-px bg-border"></div>
                          <div>
                            <p className="text-lg sm:text-xl font-semibold text-foreground">
                              USD {getWalletBalance(selectedWallet, "USD").toLocaleString()}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm sm:text-base">Select Currency to Send From</Label>
                        <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              name="currency"
                              value="ZWL"
                              checked={selectedCurrency === "ZWL"}
                              onChange={(e) => setSelectedCurrency(e.target.value as "ZWL" | "USD")}
                              className="h-4 w-4 border-primary text-primary"
                            />
                            <span className="text-sm sm:text-base">
                              ZWL Balance ({getWalletBalance(selectedWallet, "ZWL").toLocaleString()})
                            </span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              name="currency"
                              value="USD"
                              checked={selectedCurrency === "USD"}
                              onChange={(e) => setSelectedCurrency(e.target.value as "ZWL" | "USD")}
                              className="h-4 w-4 border-primary text-primary"
                            />
                            <span className="text-sm sm:text-base">
                              USD Balance ({getWalletBalance(selectedWallet, "USD").toLocaleString()})
                            </span>
                          </label>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="space-y-2 sm:space-y-3">
                <CardTitle className="text-lg sm:text-xl">Recipient Details</CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  Enter the details of where you want to send money
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Tabs
                  defaultValue="wallet"
                  className="w-full"
                  onValueChange={(value) => setRecipientType(value as RecipientType)}
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="wallet" className="text-sm sm:text-base">Mobile Wallet</TabsTrigger>
                    <TabsTrigger value="bank" className="text-sm sm:text-base">Bank Account</TabsTrigger>
                  </TabsList>

                  {/* Mobile Wallet Tab Content */}
                  <TabsContent value="wallet" className="space-y-4 mt-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="wallet-type" className="text-sm sm:text-base">Wallet Type</Label>
                        <DropdownMenu open={walletTypeOpen} onOpenChange={setWalletTypeOpen}>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="outline"
                              className={`w-full justify-between ${!selectedWalletType ? 'text-muted-foreground' : ''}`}
                              id="wallet-type"
                            >
                              {selectedWalletTypeData ? (
                                <div className="flex items-center gap-2">
                                  <div className="relative h-5 w-5">
                                    <Image
                                      src={selectedWalletTypeData.logo}
                                      alt={selectedWalletTypeData.name}
                                      fill
                                      className="object-contain"
                                    />
                                  </div>
                                  <span className="text-sm sm:text-base">{selectedWalletTypeData.name}</span>
                                </div>
                              ) : (
                                <span className="text-sm sm:text-base">Select Wallet Type</span>
                              )}
                              <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent className="w-full" align="start">
                            {walletTypes.map((wallet) => (
                              <DropdownMenuItem
                                key={wallet.id}
                                className="flex cursor-pointer items-center justify-between p-3"
                                onClick={() => {
                                  setSelectedWalletType(wallet.id);
                                  setWalletTypeOpen(false);
                                }}
                              >
                                <div className="flex items-center gap-3">
                                  <div className="relative h-6 w-6 overflow-hidden rounded-md">
                                    <Image
                                      src={wallet.logo}
                                      alt={wallet.name}
                                      fill
                                      className="object-contain"
                                    />
                                  </div>
                                  <div className="font-medium text-sm sm:text-base">{wallet.name}</div>
                                </div>
                                {selectedWalletType === wallet.id && (
                                  <CheckIcon className="h-4 w-4 text-primary" />
                                )}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone-number" className="text-sm sm:text-base">Phone Number</Label>
                        <Input
                          id="phone-number"
                          placeholder="e.g. **********"
                          value={phoneNumber}
                          onChange={(e) => setPhoneNumber(e.target.value)}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </TabsContent>

                  {/* Bank Account Tab Content */}
                  <TabsContent value="bank" className="space-y-4 mt-4">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="bank" className="text-sm sm:text-base">Select Bank</Label>
                        <DropdownMenu open={bankOpen} onOpenChange={setBankOpen}>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="outline"
                              className={`w-full justify-between ${!selectedBank ? 'text-muted-foreground' : ''}`}
                              id="bank"
                            >
                              {selectedBankData ? (
                                <div className="flex items-center gap-2">
                                  <div className="relative h-5 w-5">
                                    <Image
                                      src={selectedBankData.logo}
                                      alt={selectedBankData.name}
                                      fill
                                      className="object-contain"
                                    />
                                  </div>
                                  <span className="text-sm sm:text-base">{selectedBankData.name}</span>
                                </div>
                              ) : (
                                <span className="text-sm sm:text-base">Select Bank</span>
                              )}
                              <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent className="w-full" align="start">
                            {banks.map((bank) => (
                              <DropdownMenuItem
                                key={bank.id}
                                className="flex cursor-pointer items-center justify-between p-3"
                                onClick={() => {
                                  setSelectedBank(bank.id);
                                  setBankOpen(false);
                                }}
                              >
                                <div className="flex items-center gap-3">
                                  <div className="relative h-6 w-6 overflow-hidden rounded-md">
                                    <Image
                                      src={bank.logo}
                                      alt={bank.name}
                                      fill
                                      className="object-contain"
                                    />
                                  </div>
                                  <div className="font-medium text-sm sm:text-base">{bank.name}</div>
                                </div>
                                {selectedBank === bank.id && (
                                  <CheckIcon className="h-4 w-4 text-primary" />
                                )}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="account-number" className="text-sm sm:text-base">Account Number</Label>
                        <Input
                          id="account-number"
                          placeholder="e.g. ***********"
                          value={accountNumber}
                          onChange={(e) => setAccountNumber(e.target.value)}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="account-name" className="text-sm sm:text-base">Account Holder Name</Label>
                        <Input
                          id="account-name"
                          placeholder="e.g. John Smith"
                          value={accountName}
                          onChange={(e) => setAccountName(e.target.value)}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="branch-code" className="text-sm sm:text-base">Branch Code (Optional)</Label>
                        <Input
                          id="branch-code"
                          placeholder="e.g. 4583"
                          value={branchCode}
                          onChange={(e) => setBranchCode(e.target.value)}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="space-y-2">
                  <Label htmlFor="amount" className="text-sm sm:text-base">Amount ({selectedCurrency})</Label>
                  <Input
                    id="amount"
                    type="number"
                    placeholder={`Enter amount in ${selectedCurrency}`}
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="w-full"
                  />
                  {amount && selectedWallet && Number(amount) > getWalletBalance(selectedWallet, selectedCurrency) && (
                    <p className="mt-1 text-xs text-destructive">
                      Amount exceeds available {selectedCurrency} balance
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reason" className="text-sm sm:text-base">Reason (Optional)</Label>
                  <Input
                    id="reason"
                    placeholder="e.g. Rent payment"
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="w-full"
                  />
                </div>
              </CardContent>
              <CardFooter className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-4">
                <Button variant="outline" className="w-full sm:w-auto">Cancel</Button>
                <Button
                  className="w-full sm:w-auto gap-2"
                  disabled={!isFormValid}
                >
                  <span>Continue</span>
                  <ArrowRightIcon className="h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  );
}

export default function SendMoneyPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SendMoneyContent />
    </Suspense>
  );
}
