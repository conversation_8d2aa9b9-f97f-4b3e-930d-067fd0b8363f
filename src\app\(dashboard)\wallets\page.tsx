'use client'

import { WalletList } from '@/components/WalletList'
import { But<PERSON> } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'

export default function WalletsPage() {
  const router = useRouter()

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            onClick={() => router.push('/wallets/add')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add New Wallet
          </Button>
        </motion.div>
      </div>
      <WalletList />
    </div>
  )
} 