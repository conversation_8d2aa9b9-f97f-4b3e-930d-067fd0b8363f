import { Transaction } from "@/components/RecentTransactions";
import { WalletType, WalletConnectionState } from "./stores/wallet-store";

// Define wallet interface with multi-currency support
export interface Wallet {
  id: string;
  name: string;
  balances: {
    ZWL: number;
    USD: number;
  };
  primaryCurrency: "ZWL" | "USD";
  logo: string;
  logoAlt: string;
  isConnected: boolean;
  isPrimary?: boolean;
  description?: string;
  type: WalletType;
  state: WalletConnectionState;
}

// Mock wallet data with multi-currency support
export const wallets: Wallet[] = [
  {
    id: "ecocash-001",
    name: "EcoCash",
    balances: {
      ZWL: 5325.75,
      USD: 45.25
    },
    primaryCurrency: "ZWL",
    logo: "/images/wallets/ecocash.png",
    logoAlt: "EcoCash Logo",
    isConnected: true,
    type: "mobile",
    state: "connected"
  },
  {
    id: "onemoney-001",
    name: "<PERSON><PERSON><PERSON>",
    balances: {
      ZWL: 2150.50,
      USD: 18.75
    },
    primaryCurrency: "ZWL",
    logo: "/images/wallets/onemoney.png",
    logoAlt: "OneMoney Logo",
    isConnected: true,
    type: "mobile",
    state: "connected"
  },
  {
    id: "innbucks-001",
    name: "InnBucks",
    balances: {
      ZWL: 1875.25,
      USD: 16.30
    },
    primaryCurrency: "ZWL",
    logo: "/images/wallets/innbucks.png",
    logoAlt: "InnBucks Logo",
    isConnected: true,
    type: "mobile",
    state: "connected"
  },
  {
    id: "omari-001",
    name: "O'mari",
    balances: {
      ZWL: 950.00,
      USD: 8.25
    },
    primaryCurrency: "ZWL",
    logo: "/images/wallets/omari-logo.png",
    logoAlt: "O'mari Logo",
    isConnected: false,
    type: "mobile",
    state: "disconnected"
  },
  {
    id: "zbuniwallet-001",
    name: "ZB UniWallet",
    balances: {
      ZWL: 3750.80,
      USD: 175.50
    },
    primaryCurrency: "USD",
    logo: "/images/zb-logo.png",
    logoAlt: "ZB UniWallet Logo",
    isConnected: true,
    isPrimary: true,
    description: "All-in-one universal wallet with lowest fees",
    type: "bank",
    state: "connected"
  },
];

// Mock transaction data for the digital wallet dashboard
export const transactions: Transaction[] = [
  {
    id: "tx-001",
    type: "incoming",
    amount: 500.00,
    currency: "ZWL",
    description: "Received from John Doe",
    timestamp: new Date("2025-03-22"),
    status: "completed",
    sender: "John Doe",
    recipient: "Your Wallet"
  },
  {
    id: "tx-002",
    type: "outgoing",
    amount: 250.50,
    currency: "ZWL",
    description: "Payment to ABC Store",
    timestamp: new Date("2025-03-21"),
    status: "completed",
    sender: "Your Wallet",
    recipient: "ABC Store"
  },
  {
    id: "tx-003",
    type: "incoming",
    amount: 1000.00,
    currency: "ZWL",
    description: "Salary Payment",
    timestamp: new Date("2025-03-20"),
    status: "completed",
    sender: "Your Employer",
    recipient: "Your Wallet"
  },
  {
    id: "tx-004",
    type: "outgoing",
    amount: 350.00,
    currency: "ZWL",
    description: "Electricity Bill",
    timestamp: new Date("2025-03-19"),
    status: "pending",
    sender: "Your Wallet",
    recipient: "ZESA"
  },
  {
    id: "tx-005",
    type: "outgoing",
    amount: 125.75,
    currency: "ZWL",
    description: "Internet Subscription",
    timestamp: new Date("2025-03-18"),
    status: "completed",
    sender: "Your Wallet",
    recipient: "Internet Provider"
  },
  {
    id: "tx-006",
    type: "incoming",
    amount: 50.00,
    currency: "USD",
    description: "ZB UniWallet Bonus",
    timestamp: new Date("2025-03-17"),
    status: "completed",
    sender: "ZB UniWallet",
    recipient: "Your Wallet"
  }
];

// Mock virtual card data
export const virtualCards = [
  {
    id: "card-001",
    cardNumber: "****************",
    expiryDate: "09/28",
    cardholderName: "JOHN DOE",
    cvv: "123",
    cardType: "visa" as const,
    status: "active" as const,
    balances: {
      ZWL: 850.25,
      USD: 7.40
    },
    primaryCurrency: "ZWL",
    linkedWallet: "ecocash-001"
  },
  {
    id: "card-002",
    cardNumber: "****************",
    expiryDate: "05/27",
    cardholderName: "JOHN DOE",
    cvv: "456",
    cardType: "mastercard" as const,
    status: "inactive" as const,
    balances: {
      ZWL: 0.00,
      USD: 0.00
    },
    primaryCurrency: "ZWL",
    linkedWallet: "onemoney-001"
  },
  {
    id: "card-003",
    cardNumber: "****************",
    expiryDate: "11/29",
    cardholderName: "JOHN DOE",
    cvv: "789",
    cardType: "uniCard" as const,
    status: "active" as const,
    balances: {
      ZWL: 2500.00,
      USD: 115.75
    },
    primaryCurrency: "USD",
    linkedWallet: "zbuniwallet-001",
    features: ["Zero transaction fees", "International payments", "Instant transfers"],
    cardColor: "bg-gradient-to-r from-zb-green to-zb-lime",
    cardImage: "/images/zb-logo.png"
  }
];
