# 4. Business Logic Implementation

## 💼 **Business Logic Overview**

This document provides comprehensive implementation guidelines for the core business logic of the UniversalWallet platform, including transaction orchestration, business rules enforcement, workflow management, and enterprise-level business operations.

## 🔄 **Transaction Orchestration Implementation**

### **Saga Pattern for Distributed Transactions**
```java
@Service
@Slf4j
public class TransactionOrchestrator {
    
    private final TransactionRepository transactionRepository;
    private final ExternalProviderService externalProviderService;
    private final AccountService accountService;
    private final NotificationService notificationService;
    private final AuditService auditService;
    
    @Async
    @Transactional
    public CompletableFuture<TransactionResult> orchestrateInteroperableTransfer(Transaction transaction) {
        log.info("Starting transaction orchestration for: {}", transaction.getReferenceNumber());
        
        TransactionSaga saga = TransactionSaga.builder()
            .transactionId(transaction.getTransactionId())
            .steps(new ArrayList<>())
            .build();
        
        try {
            // Step 1: Validate and reserve funds
            SagaStep reserveFundsStep = executeReserveFunds(transaction);
            saga.getSteps().add(reserveFundsStep);
            
            if (!reserveFundsStep.isSuccess()) {
                return CompletableFuture.completedFuture(
                    TransactionResult.failure("Failed to reserve funds"));
            }
            
            // Step 2: Execute external transfer
            SagaStep externalTransferStep = executeExternalTransfer(transaction);
            saga.getSteps().add(externalTransferStep);
            
            if (!externalTransferStep.isSuccess()) {
                // Compensate: Release reserved funds
                compensateReserveFunds(transaction);
                return CompletableFuture.completedFuture(
                    TransactionResult.failure("External transfer failed"));
            }
            
            // Step 3: Confirm and finalize transaction
            SagaStep finalizeStep = finalizeTransaction(transaction);
            saga.getSteps().add(finalizeStep);
            
            if (!finalizeStep.isSuccess()) {
                // Compensate: Reverse external transfer and release funds
                compensateExternalTransfer(transaction);
                compensateReserveFunds(transaction);
                return CompletableFuture.completedFuture(
                    TransactionResult.failure("Failed to finalize transaction"));
            }
            
            // Step 4: Send notifications
            sendTransactionNotifications(transaction);
            
            log.info("Transaction orchestration completed successfully: {}", 
                transaction.getReferenceNumber());
            
            return CompletableFuture.completedFuture(
                TransactionResult.success(transaction.getReferenceNumber()));
            
        } catch (Exception e) {
            log.error("Transaction orchestration failed: {}", transaction.getReferenceNumber(), e);
            
            // Execute compensation for all completed steps
            compensateTransaction(saga);
            
            return CompletableFuture.completedFuture(
                TransactionResult.failure("System error: " + e.getMessage()));
        }
    }
    
    private SagaStep executeReserveFunds(Transaction transaction) {
        try {
            log.debug("Reserving funds for transaction: {}", transaction.getReferenceNumber());
            
            // Check account balance
            LinkedAccount sourceAccount = transaction.getSourceAccount();
            BigDecimal currentBalance = accountService.getAccountBalance(sourceAccount.getAccountId());
            
            if (currentBalance.compareTo(transaction.getTotalAmount()) < 0) {
                return SagaStep.failure("Insufficient balance");
            }
            
            // Reserve funds (create pending debit)
            accountService.reserveFunds(sourceAccount.getAccountId(), transaction.getTotalAmount(), 
                transaction.getTransactionId());
            
            // Update transaction status
            transaction.setStatus(TransactionStatus.PROCESSING);
            transactionRepository.save(transaction);
            
            return SagaStep.success("Funds reserved successfully");
            
        } catch (Exception e) {
            log.error("Failed to reserve funds for transaction: {}", transaction.getReferenceNumber(), e);
            return SagaStep.failure("Failed to reserve funds: " + e.getMessage());
        }
    }
    
    private SagaStep executeExternalTransfer(Transaction transaction) {
        try {
            log.debug("Executing external transfer for: {}", transaction.getReferenceNumber());
            
            ProviderName recipientProvider = ProviderName.valueOf(
                (String) transaction.getMetadata().get("recipient_provider"));
            
            ExternalTransferRequest request = ExternalTransferRequest.builder()
                .recipientPhone(transaction.getRecipientPhone())
                .amount(transaction.getAmount())
                .currency(transaction.getCurrency())
                .reference(transaction.getReferenceNumber())
                .description(transaction.getDescription())
                .build();
            
            ExternalTransferResult result = externalProviderService.executeTransfer(
                recipientProvider, request);
            
            if (result.isSuccess()) {
                // Update transaction with external reference
                transaction.setExternalReference(result.getExternalReference());
                transaction.getMetadata().put("external_transaction_id", result.getTransactionId());
                transactionRepository.save(transaction);
                
                return SagaStep.success("External transfer completed");
            } else {
                return SagaStep.failure("External transfer failed: " + result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("External transfer failed for transaction: {}", transaction.getReferenceNumber(), e);
            return SagaStep.failure("External transfer error: " + e.getMessage());
        }
    }
    
    private SagaStep finalizeTransaction(Transaction transaction) {
        try {
            log.debug("Finalizing transaction: {}", transaction.getReferenceNumber());
            
            // Confirm fund debit
            accountService.confirmFundReservation(transaction.getSourceAccount().getAccountId(), 
                transaction.getTransactionId());
            
            // Update transaction status
            transaction.setStatus(TransactionStatus.COMPLETED);
            transaction.setCompletedAt(LocalDateTime.now());
            transactionRepository.save(transaction);
            
            // Record audit log
            auditService.recordTransactionCompletion(transaction);
            
            return SagaStep.success("Transaction finalized");
            
        } catch (Exception e) {
            log.error("Failed to finalize transaction: {}", transaction.getReferenceNumber(), e);
            return SagaStep.failure("Finalization failed: " + e.getMessage());
        }
    }
    
    private void compensateTransaction(TransactionSaga saga) {
        log.warn("Executing compensation for transaction saga: {}", saga.getTransactionId());
        
        // Execute compensation in reverse order
        for (int i = saga.getSteps().size() - 1; i >= 0; i--) {
            SagaStep step = saga.getSteps().get(i);
            if (step.isSuccess()) {
                try {
                    executeCompensation(step, saga.getTransactionId());
                } catch (Exception e) {
                    log.error("Compensation failed for step: {}", step.getStepName(), e);
                }
            }
        }
    }
}
```

### **Business Rules Engine**
```java
@Service
@Slf4j
public class BusinessRulesEngine {
    
    private final UserRepository userRepository;
    private final TransactionRepository transactionRepository;
    private final SystemConfigurationService configService;
    
    public ValidationResult validateTransaction(UUID userId, TransactionRequest request) {
        log.debug("Validating transaction for user: {}", userId);
        
        ValidationResult result = new ValidationResult();
        
        // Get user and validate basic requirements
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new UserNotFoundException("User not found"));
        
        // Rule 1: User status validation
        if (!validateUserStatus(user, result)) {
            return result;
        }
        
        // Rule 2: Transaction limits validation
        if (!validateTransactionLimits(user, request, result)) {
            return result;
        }
        
        // Rule 3: Daily/Monthly limits validation
        if (!validatePeriodLimits(user, request, result)) {
            return result;
        }
        
        // Rule 4: KYC level requirements
        if (!validateKycRequirements(user, request, result)) {
            return result;
        }
        
        // Rule 5: Business hours validation (for certain transaction types)
        if (!validateBusinessHours(request, result)) {
            return result;
        }
        
        // Rule 6: Recipient validation
        if (!validateRecipient(request, result)) {
            return result;
        }
        
        result.setValid(true);
        return result;
    }
    
    private boolean validateUserStatus(User user, ValidationResult result) {
        if (user.getStatus() == UserStatus.SUSPENDED) {
            result.addError("USER_SUSPENDED", "Account is suspended");
            return false;
        }
        
        if (user.getStatus() == UserStatus.BLOCKED) {
            result.addError("USER_BLOCKED", "Account is blocked");
            return false;
        }
        
        if (user.getAccountLockedUntil() != null && 
            user.getAccountLockedUntil().isAfter(LocalDateTime.now())) {
            result.addError("ACCOUNT_LOCKED", "Account is temporarily locked");
            return false;
        }
        
        return true;
    }
    
    private boolean validateTransactionLimits(User user, TransactionRequest request, ValidationResult result) {
        TransactionLimits limits = getTransactionLimits(user);
        
        // Single transaction limit
        if (request.getAmount().compareTo(limits.getSingleTransactionLimit()) > 0) {
            result.addError("AMOUNT_EXCEEDS_LIMIT", 
                String.format("Amount exceeds single transaction limit of %s", 
                    limits.getSingleTransactionLimit()));
            return false;
        }
        
        // Minimum amount validation
        BigDecimal minimumAmount = configService.getMinimumTransactionAmount();
        if (request.getAmount().compareTo(minimumAmount) < 0) {
            result.addError("AMOUNT_TOO_LOW", 
                String.format("Amount below minimum of %s", minimumAmount));
            return false;
        }
        
        return true;
    }
    
    private boolean validatePeriodLimits(User user, TransactionRequest request, ValidationResult result) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.toLocalDate().atStartOfDay();
        LocalDateTime startOfMonth = now.toLocalDate().withDayOfMonth(1).atStartOfDay();
        
        TransactionLimits limits = getTransactionLimits(user);
        
        // Daily limit validation
        BigDecimal dailySpent = transactionRepository.sumCompletedTransactionAmountSince(
            user.getUserId(), startOfDay);
        
        if (dailySpent.add(request.getAmount()).compareTo(limits.getDailyLimit()) > 0) {
            result.addError("DAILY_LIMIT_EXCEEDED", 
                String.format("Transaction would exceed daily limit of %s", limits.getDailyLimit()));
            return false;
        }
        
        // Monthly limit validation
        BigDecimal monthlySpent = transactionRepository.sumCompletedTransactionAmountSince(
            user.getUserId(), startOfMonth);
        
        if (monthlySpent.add(request.getAmount()).compareTo(limits.getMonthlyLimit()) > 0) {
            result.addError("MONTHLY_LIMIT_EXCEEDED", 
                String.format("Transaction would exceed monthly limit of %s", limits.getMonthlyLimit()));
            return false;
        }
        
        return true;
    }
    
    private boolean validateKycRequirements(User user, TransactionRequest request, ValidationResult result) {
        // Enhanced KYC required for high-value transactions
        BigDecimal enhancedKycThreshold = configService.getEnhancedKycThreshold();
        
        if (request.getAmount().compareTo(enhancedKycThreshold) > 0 && 
            user.getKycLevel() != KycLevel.ENHANCED) {
            result.addError("ENHANCED_KYC_REQUIRED", 
                "Enhanced KYC verification required for this transaction amount");
            return false;
        }
        
        // Business KYC required for business features
        if (request.getTransactionType() == TransactionType.BULK_PAYMENT && 
            user.getKycLevel() != KycLevel.BUSINESS) {
            result.addError("BUSINESS_KYC_REQUIRED", 
                "Business KYC verification required for bulk payments");
            return false;
        }
        
        return true;
    }
    
    private TransactionLimits getTransactionLimits(User user) {
        switch (user.getKycLevel()) {
            case BASIC:
                return TransactionLimits.builder()
                    .singleTransactionLimit(new BigDecimal("1000"))
                    .dailyLimit(new BigDecimal("2000"))
                    .monthlyLimit(new BigDecimal("10000"))
                    .build();
                    
            case ENHANCED:
                return TransactionLimits.builder()
                    .singleTransactionLimit(new BigDecimal("10000"))
                    .dailyLimit(new BigDecimal("25000"))
                    .monthlyLimit(new BigDecimal("100000"))
                    .build();
                    
            case BUSINESS:
                return TransactionLimits.builder()
                    .singleTransactionLimit(new BigDecimal("100000"))
                    .dailyLimit(new BigDecimal("500000"))
                    .monthlyLimit(new BigDecimal("2000000"))
                    .build();
                    
            default:
                return TransactionLimits.builder()
                    .singleTransactionLimit(new BigDecimal("500"))
                    .dailyLimit(new BigDecimal("1000"))
                    .monthlyLimit(new BigDecimal("5000"))
                    .build();
        }
    }
}
```

---

## 🏢 **Business Operations Implementation**

### **Bulk Payment Service**
```java
@Service
@Transactional
@Slf4j
public class BulkPaymentServiceImpl implements BulkPaymentService {
    
    private final BulkPaymentRepository bulkPaymentRepository;
    private final TransactionService transactionService;
    private final BusinessRulesEngine businessRulesEngine;
    private final NotificationService notificationService;
    private final FileProcessingService fileProcessingService;
    
    @Override
    public BulkPaymentResponse createBulkPayment(UUID businessId, BulkPaymentRequest request) {
        log.info("Creating bulk payment for business: {} with {} payments", 
            businessId, request.getPayments().size());
        
        // Validate business permissions
        validateBusinessPermissions(businessId, request);
        
        // Validate payment file
        List<PaymentItem> validatedPayments = validatePaymentItems(request.getPayments());
        
        // Calculate total amount and fees
        BigDecimal totalAmount = validatedPayments.stream()
            .map(PaymentItem::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal totalFees = calculateBulkPaymentFees(validatedPayments);
        BigDecimal grandTotal = totalAmount.add(totalFees);
        
        // Create bulk payment record
        BulkPayment bulkPayment = BulkPayment.builder()
            .businessId(businessId)
            .batchName(request.getBatchName())
            .paymentDate(request.getPaymentDate())
            .sourceAccountId(request.getSourceAccountId())
            .totalPayments(validatedPayments.size())
            .totalAmount(totalAmount)
            .totalFees(totalFees)
            .grandTotal(grandTotal)
            .status(BulkPaymentStatus.PENDING_APPROVAL)
            .requiresApproval(request.isRequiresApproval())
            .notificationSettings(request.getNotificationSettings())
            .build();
        
        BulkPayment savedBulkPayment = bulkPaymentRepository.save(bulkPayment);
        
        // Save individual payment items
        validatedPayments.forEach(payment -> {
            payment.setBulkPaymentId(savedBulkPayment.getBulkPaymentId());
            paymentItemRepository.save(payment);
        });
        
        // Send approval notification if required
        if (request.isRequiresApproval()) {
            notificationService.sendBulkPaymentApprovalRequest(savedBulkPayment);
        } else {
            // Process immediately
            processBulkPaymentAsync(savedBulkPayment.getBulkPaymentId());
        }
        
        log.info("Bulk payment created with ID: {}", savedBulkPayment.getBulkPaymentId());
        
        return mapToBulkPaymentResponse(savedBulkPayment);
    }
    
    @Async
    @Override
    public void processBulkPaymentAsync(UUID bulkPaymentId) {
        log.info("Processing bulk payment asynchronously: {}", bulkPaymentId);
        
        BulkPayment bulkPayment = bulkPaymentRepository.findById(bulkPaymentId)
            .orElseThrow(() -> new BulkPaymentNotFoundException("Bulk payment not found"));
        
        try {
            // Update status to processing
            bulkPayment.setStatus(BulkPaymentStatus.PROCESSING);
            bulkPayment.setProcessingStartedAt(LocalDateTime.now());
            bulkPaymentRepository.save(bulkPayment);
            
            // Get payment items
            List<PaymentItem> paymentItems = paymentItemRepository.findByBulkPaymentId(bulkPaymentId);
            
            // Process payments in batches
            int batchSize = 50;
            int totalBatches = (int) Math.ceil((double) paymentItems.size() / batchSize);
            
            for (int i = 0; i < totalBatches; i++) {
                int startIndex = i * batchSize;
                int endIndex = Math.min(startIndex + batchSize, paymentItems.size());
                
                List<PaymentItem> batch = paymentItems.subList(startIndex, endIndex);
                processBatch(bulkPayment, batch, i + 1, totalBatches);
                
                // Small delay between batches to avoid overwhelming external systems
                Thread.sleep(1000);
            }
            
            // Update final status
            updateBulkPaymentStatus(bulkPayment);
            
            // Send completion notification
            notificationService.sendBulkPaymentCompletionNotification(bulkPayment);
            
            log.info("Bulk payment processing completed: {}", bulkPaymentId);
            
        } catch (Exception e) {
            log.error("Bulk payment processing failed: {}", bulkPaymentId, e);
            
            bulkPayment.setStatus(BulkPaymentStatus.FAILED);
            bulkPayment.setFailureReason(e.getMessage());
            bulkPayment.setProcessingCompletedAt(LocalDateTime.now());
            bulkPaymentRepository.save(bulkPayment);
            
            notificationService.sendBulkPaymentFailureNotification(bulkPayment);
        }
    }
    
    private void processBatch(BulkPayment bulkPayment, List<PaymentItem> batch, 
                             int batchNumber, int totalBatches) {
        log.debug("Processing batch {}/{} for bulk payment: {}", 
            batchNumber, totalBatches, bulkPayment.getBulkPaymentId());
        
        for (PaymentItem paymentItem : batch) {
            try {
                // Create individual transaction
                TransferRequest transferRequest = TransferRequest.builder()
                    .recipientPhone(paymentItem.getRecipientPhone())
                    .amount(paymentItem.getAmount())
                    .sourceAccountId(bulkPayment.getSourceAccountId())
                    .description(paymentItem.getDescription())
                    .reference(paymentItem.getReference())
                    .build();
                
                TransactionResponse response = transactionService.processTransfer(
                    bulkPayment.getBusinessId(), transferRequest);
                
                // Update payment item status
                paymentItem.setStatus(PaymentItemStatus.COMPLETED);
                paymentItem.setTransactionId(response.getTransactionId());
                paymentItem.setProcessedAt(LocalDateTime.now());
                
            } catch (Exception e) {
                log.error("Failed to process payment item: {}", paymentItem.getPaymentItemId(), e);
                
                paymentItem.setStatus(PaymentItemStatus.FAILED);
                paymentItem.setFailureReason(e.getMessage());
                paymentItem.setProcessedAt(LocalDateTime.now());
            }
            
            paymentItemRepository.save(paymentItem);
        }
        
        // Update bulk payment progress
        updateBulkPaymentProgress(bulkPayment);
    }
    
    private void updateBulkPaymentProgress(BulkPayment bulkPayment) {
        List<PaymentItem> allItems = paymentItemRepository.findByBulkPaymentId(
            bulkPayment.getBulkPaymentId());
        
        long completedCount = allItems.stream()
            .mapToLong(item -> item.getStatus() == PaymentItemStatus.COMPLETED ? 1 : 0)
            .sum();
        
        long failedCount = allItems.stream()
            .mapToLong(item -> item.getStatus() == PaymentItemStatus.FAILED ? 1 : 0)
            .sum();
        
        bulkPayment.setCompletedPayments((int) completedCount);
        bulkPayment.setFailedPayments((int) failedCount);
        
        // Calculate success rate
        double successRate = (double) completedCount / allItems.size() * 100;
        bulkPayment.setSuccessRate(successRate);
        
        bulkPaymentRepository.save(bulkPayment);
    }
}
```

### **Invoice Management Service**
```java
@Service
@Transactional
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {
    
    private final InvoiceRepository invoiceRepository;
    private final BusinessProfileRepository businessProfileRepository;
    private final QrCodeService qrCodeService;
    private final PaymentLinkService paymentLinkService;
    private final NotificationService notificationService;
    private final PdfGenerationService pdfGenerationService;
    
    @Override
    public InvoiceResponse createInvoice(UUID businessId, CreateInvoiceRequest request) {
        log.info("Creating invoice for business: {}", businessId);
        
        // Validate business
        BusinessProfile business = businessProfileRepository.findById(businessId)
            .orElseThrow(() -> new BusinessNotFoundException("Business not found"));
        
        // Generate invoice number
        String invoiceNumber = generateInvoiceNumber(business);
        
        // Calculate amounts
        BigDecimal subtotal = request.getLineItems().stream()
            .map(item -> item.getUnitPrice().multiply(new BigDecimal(item.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal taxAmount = subtotal.multiply(request.getTaxRate())
            .setScale(2, RoundingMode.HALF_UP);
        
        BigDecimal totalAmount = subtotal.add(taxAmount);
        
        // Create invoice
        Invoice invoice = Invoice.builder()
            .business(business)
            .invoiceNumber(invoiceNumber)
            .customerName(request.getCustomerName())
            .customerEmail(request.getCustomerEmail())
            .customerPhone(request.getCustomerPhone())
            .invoiceDate(request.getInvoiceDate())
            .dueDate(request.getDueDate())
            .subtotal(subtotal)
            .taxRate(request.getTaxRate())
            .taxAmount(taxAmount)
            .totalAmount(totalAmount)
            .currency(request.getCurrency())
            .status(InvoiceStatus.DRAFT)
            .lineItems(request.getLineItems())
            .notes(request.getNotes())
            .termsConditions(request.getTermsConditions())
            .build();
        
        Invoice savedInvoice = invoiceRepository.save(invoice);
        
        // Generate payment link and QR code
        String paymentLink = paymentLinkService.generatePaymentLink(savedInvoice);
        String qrCodeUrl = qrCodeService.generateInvoiceQrCode(savedInvoice);
        
        savedInvoice.setPaymentLink(paymentLink);
        savedInvoice.setQrCodeUrl(qrCodeUrl);
        invoiceRepository.save(savedInvoice);
        
        log.info("Invoice created with number: {}", invoiceNumber);
        
        return mapToInvoiceResponse(savedInvoice);
    }
    
    @Override
    public InvoiceResponse sendInvoice(UUID invoiceId) {
        log.info("Sending invoice: {}", invoiceId);
        
        Invoice invoice = invoiceRepository.findById(invoiceId)
            .orElseThrow(() -> new InvoiceNotFoundException("Invoice not found"));
        
        if (invoice.getStatus() != InvoiceStatus.DRAFT) {
            throw new InvalidInvoiceStatusException("Invoice has already been sent");
        }
        
        // Generate PDF
        byte[] pdfContent = pdfGenerationService.generateInvoicePdf(invoice);
        
        // Send email with PDF attachment
        notificationService.sendInvoiceEmail(invoice, pdfContent);
        
        // Update invoice status
        invoice.setStatus(InvoiceStatus.SENT);
        invoice.setSentAt(LocalDateTime.now());
        invoiceRepository.save(invoice);
        
        log.info("Invoice sent successfully: {}", invoice.getInvoiceNumber());
        
        return mapToInvoiceResponse(invoice);
    }
    
    @Override
    public void processInvoicePayment(UUID invoiceId, PaymentNotification payment) {
        log.info("Processing payment for invoice: {}", invoiceId);
        
        Invoice invoice = invoiceRepository.findById(invoiceId)
            .orElseThrow(() -> new InvoiceNotFoundException("Invoice not found"));
        
        if (invoice.getStatus() == InvoiceStatus.PAID) {
            log.warn("Invoice already paid: {}", invoiceId);
            return;
        }
        
        // Validate payment amount
        if (payment.getAmount().compareTo(invoice.getTotalAmount()) != 0) {
            throw new InvalidPaymentAmountException("Payment amount does not match invoice total");
        }
        
        // Update invoice status
        invoice.setStatus(InvoiceStatus.PAID);
        invoice.setPaidAt(LocalDateTime.now());
        invoice.setPaymentReference(payment.getPaymentReference());
        invoiceRepository.save(invoice);
        
        // Send payment confirmation
        notificationService.sendPaymentConfirmation(invoice, payment);
        
        // Notify business
        notificationService.sendInvoicePaymentNotification(invoice);
        
        log.info("Invoice payment processed successfully: {}", invoice.getInvoiceNumber());
    }
}
```

**This comprehensive business logic implementation provides enterprise-level transaction orchestration, business rules enforcement, and operational workflows for the UniversalWallet platform.** 💼
