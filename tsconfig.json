{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "types": ["node", "react", "react-dom", "zustand"]}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "build/types/**/*.ts", "next-env.d.ts", "dist/types/**/*.ts"], "exclude": ["node_modules"]}