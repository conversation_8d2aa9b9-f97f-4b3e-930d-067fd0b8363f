# 1. Core API Development

## 🚀 **Core API Overview**

This document provides comprehensive implementation guidelines for the UniversalWallet core API development, covering all essential services, controllers, business logic, and integration patterns required for Zimbabwe's unified financial platform.

## 🏗️ **API Architecture Implementation**

### **Spring Boot Application Structure**

#### **Main Application Class**
```java
@SpringBootApplication
@EnableJpaRepositories
@EnableScheduling
@EnableAsync
@EnableCaching
public class UniversalWalletApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(UniversalWalletApplication.class, args);
    }
    
    @Bean
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("UW-Async-");
        executor.initialize();
        return executor;
    }
    
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());
        return builder.build();
    }
}
```

### **Configuration Classes**

#### **Security Configuration**
```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@Slf4j
public class SecurityConfig {
    
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtRequestFilter jwtRequestFilter;
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
            .sessionManagement(session -> 
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/system/health").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                .requestMatchers(HttpMethod.POST, "/api/v1/users/register").permitAll()
                .requestMatchers("/api/v1/admin/**").hasRole("ADMIN")
                .requestMatchers("/api/v1/business/**").hasAnyRole("BUSINESS_ADMIN", "BUSINESS_USER")
                .requestMatchers("/api/v1/agent/**").hasRole("AGENT")
                .anyRequest().authenticated()
            )
            .exceptionHandling(ex -> ex.authenticationEntryPoint(jwtAuthenticationEntryPoint))
            .addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }
}
```

---

## 👤 **User Management API Implementation**

### **User Entity**
```java
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "user_id")
    private UUID userId;
    
    @Column(name = "phone_number", unique = true, nullable = false)
    @Pattern(regexp = "^\\+263[0-9]{9}$")
    private String phoneNumber;
    
    @Column(name = "email", unique = true)
    @Email
    private String email;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false)
    private UserType userType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private UserStatus status = UserStatus.PENDING;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "kyc_level")
    private KycLevel kycLevel = KycLevel.BASIC;
    
    @Column(name = "pin_hash", nullable = false)
    private String pinHash;
    
    @Column(name = "biometric_enabled")
    private Boolean biometricEnabled = false;
    
    @Column(name = "last_login_at")
    private LocalDateTime lastLoginAt;
    
    @Column(name = "failed_login_attempts")
    private Integer failedLoginAttempts = 0;
    
    @Column(name = "account_locked_until")
    private LocalDateTime accountLockedUntil;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private UserProfile profile;
    
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LinkedAccount> linkedAccounts = new ArrayList<>();
    
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Transaction> transactions = new ArrayList<>();
}
```

### **User Service Implementation**
```java
@Service
@Transactional
@Slf4j
public class UserServiceImpl implements UserService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final OtpService otpService;
    private final NotificationService notificationService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public UserRegistrationResponse registerUser(UserRegistrationRequest request) {
        log.info("Registering new user with phone: {}", maskPhoneNumber(request.getPhoneNumber()));
        
        // Check if user already exists
        if (userRepository.existsByPhoneNumber(request.getPhoneNumber())) {
            throw new UserAlreadyExistsException("User with this phone number already exists");
        }
        
        // Create user entity
        User user = User.builder()
            .phoneNumber(request.getPhoneNumber())
            .userType(request.getUserType())
            .pinHash(passwordEncoder.encode(request.getPin()))
            .status(UserStatus.PENDING)
            .kycLevel(KycLevel.BASIC)
            .build();
        
        // Save user
        User savedUser = userRepository.save(user);
        
        // Generate and send OTP
        String otp = otpService.generateOtp(savedUser.getUserId());
        notificationService.sendOtpSms(request.getPhoneNumber(), otp);
        
        // Create temporary verification token
        String verificationToken = generateVerificationToken(savedUser.getUserId());
        
        log.info("User registered successfully with ID: {}", savedUser.getUserId());
        
        return UserRegistrationResponse.builder()
            .userId(savedUser.getUserId())
            .phoneNumber(savedUser.getPhoneNumber())
            .status(savedUser.getStatus())
            .verificationToken(verificationToken)
            .build();
    }
    
    @Override
    public AuthenticationResponse verifyOtp(OtpVerificationRequest request) {
        log.info("Verifying OTP for phone: {}", maskPhoneNumber(request.getPhoneNumber()));
        
        // Find user by phone number
        User user = userRepository.findByPhoneNumber(request.getPhoneNumber())
            .orElseThrow(() -> new UserNotFoundException("User not found"));
        
        // Verify OTP
        if (!otpService.verifyOtp(user.getUserId(), request.getOtp())) {
            throw new InvalidOtpException("Invalid or expired OTP");
        }
        
        // Update user status
        user.setStatus(UserStatus.ACTIVE);
        user.setLastLoginAt(LocalDateTime.now());
        userRepository.save(user);
        
        // Generate JWT tokens
        String accessToken = jwtTokenProvider.generateAccessToken(user);
        String refreshToken = jwtTokenProvider.generateRefreshToken(user);
        
        // Store refresh token in Redis
        storeRefreshToken(user.getUserId(), refreshToken);
        
        log.info("OTP verified successfully for user: {}", user.getUserId());
        
        return AuthenticationResponse.builder()
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .expiresIn(jwtTokenProvider.getAccessTokenExpiration())
            .user(mapToUserDto(user))
            .build();
    }
    
    @Override
    public AuthenticationResponse authenticateUser(LoginRequest request) {
        log.info("Authenticating user with phone: {}", maskPhoneNumber(request.getPhoneNumber()));
        
        // Find user by phone number
        User user = userRepository.findByPhoneNumber(request.getPhoneNumber())
            .orElseThrow(() -> new UserNotFoundException("Invalid credentials"));
        
        // Check account status
        validateUserAccount(user);
        
        // Verify PIN
        if (!passwordEncoder.matches(request.getPin(), user.getPinHash())) {
            handleFailedLogin(user);
            throw new InvalidCredentialsException("Invalid credentials");
        }
        
        // Reset failed login attempts
        user.setFailedLoginAttempts(0);
        user.setAccountLockedUntil(null);
        user.setLastLoginAt(LocalDateTime.now());
        userRepository.save(user);
        
        // Generate JWT tokens
        String accessToken = jwtTokenProvider.generateAccessToken(user);
        String refreshToken = jwtTokenProvider.generateRefreshToken(user);
        
        // Store refresh token
        storeRefreshToken(user.getUserId(), refreshToken);
        
        log.info("User authenticated successfully: {}", user.getUserId());
        
        return AuthenticationResponse.builder()
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .expiresIn(jwtTokenProvider.getAccessTokenExpiration())
            .user(mapToUserDto(user))
            .build();
    }
    
    private void validateUserAccount(User user) {
        if (user.getStatus() == UserStatus.SUSPENDED) {
            throw new AccountSuspendedException("Account is suspended");
        }
        
        if (user.getAccountLockedUntil() != null && 
            user.getAccountLockedUntil().isAfter(LocalDateTime.now())) {
            throw new AccountLockedException("Account is temporarily locked");
        }
    }
    
    private void handleFailedLogin(User user) {
        user.setFailedLoginAttempts(user.getFailedLoginAttempts() + 1);
        
        if (user.getFailedLoginAttempts() >= 5) {
            user.setAccountLockedUntil(LocalDateTime.now().plusMinutes(30));
            log.warn("Account locked due to multiple failed login attempts: {}", user.getUserId());
        }
        
        userRepository.save(user);
    }
}
```

### **User Controller**
```java
@RestController
@RequestMapping("/api/v1/users")
@Validated
@Slf4j
@Tag(name = "User Management", description = "User registration, authentication, and profile management")
public class UserController {
    
    private final UserService userService;
    
    @PostMapping("/register")
    @Operation(summary = "Register new user", description = "Register a new user with phone number and PIN")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "User registered successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "409", description = "User already exists")
    })
    public ResponseEntity<ApiResponse<UserRegistrationResponse>> registerUser(
            @Valid @RequestBody UserRegistrationRequest request) {
        
        log.info("User registration request received for phone: {}", 
                maskPhoneNumber(request.getPhoneNumber()));
        
        UserRegistrationResponse response = userService.registerUser(request);
        
        return ResponseEntity.ok(ApiResponse.<UserRegistrationResponse>builder()
            .success(true)
            .data(response)
            .message("User registered successfully. Please verify OTP.")
            .timestamp(Instant.now())
            .build());
    }
    
    @PostMapping("/verify-otp")
    @Operation(summary = "Verify OTP", description = "Verify OTP and complete user registration")
    public ResponseEntity<ApiResponse<AuthenticationResponse>> verifyOtp(
            @Valid @RequestBody OtpVerificationRequest request) {
        
        AuthenticationResponse response = userService.verifyOtp(request);
        
        return ResponseEntity.ok(ApiResponse.<AuthenticationResponse>builder()
            .success(true)
            .data(response)
            .message("OTP verified successfully")
            .timestamp(Instant.now())
            .build());
    }
    
    @PostMapping("/login")
    @Operation(summary = "User login", description = "Authenticate user with phone number and PIN")
    public ResponseEntity<ApiResponse<AuthenticationResponse>> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        // Log login attempt with IP address
        String ipAddress = getClientIpAddress(httpRequest);
        log.info("Login attempt from IP: {} for phone: {}", 
                ipAddress, maskPhoneNumber(request.getPhoneNumber()));
        
        AuthenticationResponse response = userService.authenticateUser(request);
        
        return ResponseEntity.ok(ApiResponse.<AuthenticationResponse>builder()
            .success(true)
            .data(response)
            .message("Login successful")
            .timestamp(Instant.now())
            .build());
    }
    
    @GetMapping("/profile")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "Get user profile", description = "Get current user's profile information")
    public ResponseEntity<ApiResponse<UserProfileResponse>> getUserProfile(
            Authentication authentication) {
        
        UUID userId = getUserIdFromAuthentication(authentication);
        UserProfileResponse response = userService.getUserProfile(userId);
        
        return ResponseEntity.ok(ApiResponse.<UserProfileResponse>builder()
            .success(true)
            .data(response)
            .timestamp(Instant.now())
            .build());
    }
    
    @PutMapping("/profile")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "Update user profile", description = "Update user's profile information")
    public ResponseEntity<ApiResponse<UserProfileResponse>> updateUserProfile(
            @Valid @RequestBody UpdateProfileRequest request,
            Authentication authentication) {
        
        UUID userId = getUserIdFromAuthentication(authentication);
        UserProfileResponse response = userService.updateUserProfile(userId, request);
        
        return ResponseEntity.ok(ApiResponse.<UserProfileResponse>builder()
            .success(true)
            .data(response)
            .message("Profile updated successfully")
            .timestamp(Instant.now())
            .build());
    }
    
    @PostMapping("/change-pin")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "Change PIN", description = "Change user's transaction PIN")
    public ResponseEntity<ApiResponse<Void>> changePin(
            @Valid @RequestBody ChangePinRequest request,
            Authentication authentication) {
        
        UUID userId = getUserIdFromAuthentication(authentication);
        userService.changePin(userId, request);
        
        return ResponseEntity.ok(ApiResponse.<Void>builder()
            .success(true)
            .message("PIN changed successfully")
            .timestamp(Instant.now())
            .build());
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
    
    private UUID getUserIdFromAuthentication(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        return userPrincipal.getUserId();
    }
    
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        return phoneNumber.substring(0, 4) + "****" + 
               phoneNumber.substring(phoneNumber.length() - 2);
    }
}
```

---

## 💰 **Account Management API Implementation**

### **Linked Account Entity**
```java
@Entity
@Table(name = "linked_accounts")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LinkedAccount {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "account_id")
    private UUID accountId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "provider_name", nullable = false)
    private ProviderName providerName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "account_type", nullable = false)
    private AccountType accountType;
    
    @Column(name = "account_number")
    private String accountNumber;
    
    @Column(name = "account_name")
    private String accountName;
    
    @Column(name = "currency")
    private String currency = "ZWG";
    
    @Column(name = "is_primary")
    private Boolean isPrimary = false;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "last_balance", precision = 15, scale = 2)
    private BigDecimal lastBalance = BigDecimal.ZERO;
    
    @Column(name = "last_balance_update")
    private LocalDateTime lastBalanceUpdate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "connection_status")
    private ConnectionStatus connectionStatus = ConnectionStatus.PENDING;
    
    @Type(JsonType.class)
    @Column(name = "connection_metadata", columnDefinition = "jsonb")
    private Map<String, Object> connectionMetadata = new HashMap<>();
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

### **Account Service Implementation**
```java
@Service
@Transactional
@Slf4j
public class AccountServiceImpl implements AccountService {
    
    private final LinkedAccountRepository linkedAccountRepository;
    private final ExternalProviderService externalProviderService;
    private final EncryptionService encryptionService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public LinkAccountResponse linkAccount(UUID userId, LinkAccountRequest request) {
        log.info("Linking account for user: {} with provider: {}", userId, request.getProviderName());
        
        // Validate if account already linked
        if (linkedAccountRepository.existsByUserIdAndProviderNameAndAccountNumber(
                userId, request.getProviderName(), request.getAccountNumber())) {
            throw new AccountAlreadyLinkedException("Account already linked");
        }
        
        // Verify account with external provider
        ExternalAccountVerification verification = externalProviderService
            .verifyAccount(request.getProviderName(), request.getAccountNumber(), request.getCredentials());
        
        if (!verification.isValid()) {
            throw new InvalidAccountException("Unable to verify account with provider");
        }
        
        // Create linked account
        LinkedAccount linkedAccount = LinkedAccount.builder()
            .user(userRepository.getReferenceById(userId))
            .providerName(request.getProviderName())
            .accountType(request.getAccountType())
            .accountNumber(request.getAccountNumber())
            .accountName(verification.getAccountName())
            .currency(request.getCurrency())
            .connectionStatus(ConnectionStatus.CONNECTED)
            .lastBalance(verification.getBalance())
            .lastBalanceUpdate(LocalDateTime.now())
            .build();
        
        // Encrypt and store credentials
        String encryptedCredentials = encryptionService.encrypt(request.getCredentials());
        linkedAccount.getConnectionMetadata().put("encrypted_credentials", encryptedCredentials);
        
        LinkedAccount savedAccount = linkedAccountRepository.save(linkedAccount);
        
        // Cache account balance
        cacheAccountBalance(savedAccount.getAccountId(), verification.getBalance());
        
        log.info("Account linked successfully: {}", savedAccount.getAccountId());
        
        return LinkAccountResponse.builder()
            .accountId(savedAccount.getAccountId())
            .providerName(savedAccount.getProviderName())
            .accountNumber(savedAccount.getAccountNumber())
            .accountName(savedAccount.getAccountName())
            .status(savedAccount.getConnectionStatus())
            .balance(savedAccount.getLastBalance())
            .currency(savedAccount.getCurrency())
            .linkedAt(savedAccount.getCreatedAt())
            .build();
    }
    
    @Override
    public AccountBalanceResponse getAccountBalances(UUID userId) {
        log.debug("Getting account balances for user: {}", userId);
        
        List<LinkedAccount> accounts = linkedAccountRepository.findByUserIdAndIsActiveTrue(userId);
        
        BigDecimal totalBalance = BigDecimal.ZERO;
        List<AccountBalanceDto> accountBalances = new ArrayList<>();
        
        for (LinkedAccount account : accounts) {
            // Try to get fresh balance from cache or external provider
            BigDecimal currentBalance = refreshAccountBalance(account);
            totalBalance = totalBalance.add(currentBalance);
            
            accountBalances.add(AccountBalanceDto.builder()
                .accountId(account.getAccountId())
                .providerName(account.getProviderName())
                .accountType(account.getAccountType())
                .balance(currentBalance)
                .availableBalance(currentBalance) // TODO: Calculate available balance
                .isPrimary(account.getIsPrimary())
                .status(account.getConnectionStatus())
                .lastUpdated(account.getLastBalanceUpdate())
                .build());
        }
        
        return AccountBalanceResponse.builder()
            .totalBalance(totalBalance)
            .currency("ZWG") // TODO: Handle multi-currency
            .accounts(accountBalances)
            .lastUpdated(LocalDateTime.now())
            .build();
    }
    
    @Async
    @Override
    public CompletableFuture<BigDecimal> refreshAccountBalance(LinkedAccount account) {
        try {
            // Check cache first
            String cacheKey = "account_balance:" + account.getAccountId();
            BigDecimal cachedBalance = (BigDecimal) redisTemplate.opsForValue().get(cacheKey);
            
            if (cachedBalance != null && 
                account.getLastBalanceUpdate().isAfter(LocalDateTime.now().minusMinutes(5))) {
                return CompletableFuture.completedFuture(cachedBalance);
            }
            
            // Fetch from external provider
            String credentials = encryptionService.decrypt(
                (String) account.getConnectionMetadata().get("encrypted_credentials"));
            
            BigDecimal freshBalance = externalProviderService.getAccountBalance(
                account.getProviderName(), account.getAccountNumber(), credentials);
            
            // Update account record
            account.setLastBalance(freshBalance);
            account.setLastBalanceUpdate(LocalDateTime.now());
            linkedAccountRepository.save(account);
            
            // Update cache
            cacheAccountBalance(account.getAccountId(), freshBalance);
            
            return CompletableFuture.completedFuture(freshBalance);
            
        } catch (Exception e) {
            log.error("Failed to refresh balance for account: {}", account.getAccountId(), e);
            return CompletableFuture.completedFuture(account.getLastBalance());
        }
    }
    
    private void cacheAccountBalance(UUID accountId, BigDecimal balance) {
        String cacheKey = "account_balance:" + accountId;
        redisTemplate.opsForValue().set(cacheKey, balance, Duration.ofMinutes(5));
    }
}
```

---

## 💸 **Transaction Processing API Implementation**

### **Transaction Entity**
```java
@Entity
@Table(name = "transactions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Transaction {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "transaction_id")
    private UUID transactionId;

    @Column(name = "reference_number", unique = true, nullable = false)
    private String referenceNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false)
    private TransactionType transactionType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private TransactionStatus status = TransactionStatus.PENDING;

    @Column(name = "amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal amount;

    @Column(name = "currency")
    private String currency = "ZWG";

    @Column(name = "fee_amount", precision = 15, scale = 2)
    private BigDecimal feeAmount = BigDecimal.ZERO;

    @Column(name = "total_amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal totalAmount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "source_account_id")
    private LinkedAccount sourceAccount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destination_account_id")
    private LinkedAccount destinationAccount;

    @Column(name = "recipient_phone")
    private String recipientPhone;

    @Column(name = "recipient_name")
    private String recipientName;

    @Column(name = "description")
    private String description;

    @Type(JsonType.class)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata = new HashMap<>();

    @Column(name = "external_reference")
    private String externalReference;

    @CreationTimestamp
    @Column(name = "initiated_at")
    private LocalDateTime initiatedAt;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    @Column(name = "failed_at")
    private LocalDateTime failedAt;

    @Column(name = "failure_reason")
    private String failureReason;

    @OneToMany(mappedBy = "transaction", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TransactionLeg> transactionLegs = new ArrayList<>();
}
```

### **Transaction Service Implementation**
```java
@Service
@Transactional
@Slf4j
public class TransactionServiceImpl implements TransactionService {

    private final TransactionRepository transactionRepository;
    private final LinkedAccountRepository linkedAccountRepository;
    private final ExternalProviderService externalProviderService;
    private final FeeCalculationService feeCalculationService;
    private final FraudDetectionService fraudDetectionService;
    private final NotificationService notificationService;
    private final TransactionOrchestrator transactionOrchestrator;

    @Override
    public TransactionResponse processTransfer(UUID userId, TransferRequest request) {
        log.info("Processing transfer for user: {} to recipient: {}",
                userId, maskPhoneNumber(request.getRecipientPhone()));

        // Validate request
        validateTransferRequest(request);

        // Check user limits
        validateTransactionLimits(userId, request.getAmount());

        // Fraud detection
        FraudAssessment fraudAssessment = fraudDetectionService.assessTransaction(userId, request);
        if (fraudAssessment.getRiskScore() > 70) {
            throw new SuspiciousActivityException("Transaction flagged for manual review");
        }

        // Get source account
        LinkedAccount sourceAccount = linkedAccountRepository.findById(request.getSourceAccountId())
            .orElseThrow(() -> new AccountNotFoundException("Source account not found"));

        // Validate account ownership
        if (!sourceAccount.getUser().getUserId().equals(userId)) {
            throw new UnauthorizedAccountAccessException("Account does not belong to user");
        }

        // Calculate fees
        BigDecimal feeAmount = feeCalculationService.calculateTransferFee(request);
        BigDecimal totalAmount = request.getAmount().add(feeAmount);

        // Check balance
        if (sourceAccount.getLastBalance().compareTo(totalAmount) < 0) {
            throw new InsufficientBalanceException("Insufficient balance for transaction");
        }

        // Create transaction record
        Transaction transaction = Transaction.builder()
            .referenceNumber(generateReferenceNumber())
            .user(sourceAccount.getUser())
            .transactionType(TransactionType.INTEROPERABLE_TRANSFER)
            .amount(request.getAmount())
            .feeAmount(feeAmount)
            .totalAmount(totalAmount)
            .sourceAccount(sourceAccount)
            .recipientPhone(request.getRecipientPhone())
            .description(request.getDescription())
            .status(TransactionStatus.PENDING)
            .build();

        // Detect recipient provider
        ProviderName recipientProvider = detectRecipientProvider(request.getRecipientPhone());
        transaction.getMetadata().put("recipient_provider", recipientProvider.name());

        Transaction savedTransaction = transactionRepository.save(transaction);

        // Process transaction asynchronously
        transactionOrchestrator.processTransferAsync(savedTransaction);

        log.info("Transfer initiated with reference: {}", savedTransaction.getReferenceNumber());

        return mapToTransactionResponse(savedTransaction);
    }

    @Async
    @Override
    public void processTransferAsync(Transaction transaction) {
        try {
            log.info("Processing transaction asynchronously: {}", transaction.getReferenceNumber());

            // Update status to processing
            transaction.setStatus(TransactionStatus.PROCESSING);
            transactionRepository.save(transaction);

            // Execute transfer based on recipient provider
            ProviderName recipientProvider = ProviderName.valueOf(
                (String) transaction.getMetadata().get("recipient_provider"));

            TransferResult result = executeExternalTransfer(transaction, recipientProvider);

            if (result.isSuccess()) {
                // Update transaction status
                transaction.setStatus(TransactionStatus.COMPLETED);
                transaction.setCompletedAt(LocalDateTime.now());
                transaction.setExternalReference(result.getExternalReference());

                // Update source account balance
                updateAccountBalance(transaction.getSourceAccount(),
                    transaction.getSourceAccount().getLastBalance().subtract(transaction.getTotalAmount()));

                // Send success notification
                notificationService.sendTransferSuccessNotification(transaction);

                log.info("Transaction completed successfully: {}", transaction.getReferenceNumber());

            } else {
                // Handle failure
                transaction.setStatus(TransactionStatus.FAILED);
                transaction.setFailedAt(LocalDateTime.now());
                transaction.setFailureReason(result.getFailureReason());

                // Send failure notification
                notificationService.sendTransferFailureNotification(transaction);

                log.error("Transaction failed: {} - {}",
                    transaction.getReferenceNumber(), result.getFailureReason());
            }

            transactionRepository.save(transaction);

        } catch (Exception e) {
            log.error("Error processing transaction: {}", transaction.getReferenceNumber(), e);

            // Update transaction status to failed
            transaction.setStatus(TransactionStatus.FAILED);
            transaction.setFailedAt(LocalDateTime.now());
            transaction.setFailureReason("System error: " + e.getMessage());
            transactionRepository.save(transaction);

            // Send error notification
            notificationService.sendTransferErrorNotification(transaction);
        }
    }

    private TransferResult executeExternalTransfer(Transaction transaction, ProviderName recipientProvider) {
        switch (recipientProvider) {
            case ECOCASH:
                return externalProviderService.sendToEcoCash(
                    transaction.getRecipientPhone(),
                    transaction.getAmount(),
                    transaction.getReferenceNumber()
                );

            case ONEMONEY:
                return externalProviderService.sendToOneMoney(
                    transaction.getRecipientPhone(),
                    transaction.getAmount(),
                    transaction.getReferenceNumber()
                );

            case UNIVERSALWALLET:
                return processInternalTransfer(transaction);

            default:
                throw new UnsupportedProviderException("Provider not supported: " + recipientProvider);
        }
    }

    private TransferResult processInternalTransfer(Transaction transaction) {
        // Find recipient's UniversalWallet account
        User recipient = userRepository.findByPhoneNumber(transaction.getRecipientPhone())
            .orElseThrow(() -> new RecipientNotFoundException("Recipient not found"));

        LinkedAccount recipientAccount = linkedAccountRepository
            .findByUserAndProviderName(recipient, ProviderName.UNIVERSALWALLET)
            .orElseThrow(() -> new AccountNotFoundException("Recipient account not found"));

        // Credit recipient account
        updateAccountBalance(recipientAccount,
            recipientAccount.getLastBalance().add(transaction.getAmount()));

        // Update transaction with destination account
        transaction.setDestinationAccount(recipientAccount);
        transaction.setRecipientName(recipient.getProfile().getFirstName() + " " +
                                   recipient.getProfile().getLastName());

        return TransferResult.success(transaction.getReferenceNumber());
    }
}
```

**This comprehensive core API implementation provides the foundation for all UniversalWallet platform functionality with enterprise-level security, performance, and reliability.** 🚀
