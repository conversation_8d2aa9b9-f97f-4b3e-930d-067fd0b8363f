import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRightIcon, CheckIcon, ArrowUpDownIcon, ShieldCheckIcon, SmartphoneIcon, CreditCardIcon, BarChart3Icon } from "lucide-react";

export default function LandingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur">
        <div className="container flex h-16 items-center justify-between py-4">
          <Link href="/" className="flex items-center gap-2">
            <Image
              src="/images/zb-logo.png"
              alt="ZB Digital Wallet"
              width={40}
              height={40}
              className="h-8 w-auto"
            />
            <span className="font-semibold">ZB Digital Wallet</span>
          </Link>
          <nav className="hidden gap-6 md:flex">
            <Link
              href="#features"
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              Features
            </Link>
            <Link
              href="#benefits"
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              Benefits
            </Link>
            <Link
              href="#wallets"
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              Supported Wallets
            </Link>
            <Link
              href="#faq"
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              FAQ
            </Link>
          </nav>
          <div className="flex items-center gap-2">
            <Link href="/login">
              <Button variant="ghost">Log In</Button>
            </Link>
            <Link href="/register">
              <Button>Register</Button>
            </Link>
          </div>
        </div>
      </header>
      <main className="flex-1">
        <section className="w-full bg-gradient-to-b from-primary/10 to-background px-4 py-16 md:py-24 lg:py-32">
          <div className="container flex flex-col items-center gap-6 text-center">
            <h1 className="text-3xl font-bold sm:text-4xl md:text-5xl lg:text-6xl">
              Your Universal Digital{" "}
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Wallet
              </span>
            </h1>
            <p className="max-w-[42rem] text-lg text-muted-foreground sm:text-xl">
              A seamless way to manage and transact across multiple digital money
              platforms in Zimbabwe without relying on ZIMSWITCH.
            </p>
            <div className="flex flex-wrap items-center justify-center gap-4">
              <Link href="/register">
                <Button size="lg" className="gap-2">
                  Get Started
                  <ArrowRightIcon className="h-4 w-4" />
                </Button>
              </Link>
              <Link href="#features">
                <Button variant="outline" size="lg">
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <section
          id="features"
          className="w-full bg-muted/30 px-4 py-16 md:py-24 lg:py-32"
        >
          <div className="container space-y-12">
            <div className="space-y-4 text-center">
              <h2 className="text-3xl font-bold sm:text-4xl">Key Features</h2>
              <p className="mx-auto max-w-[42rem] text-muted-foreground">
                Our digital wallet provides seamless integration with all your
                existing digital money platforms.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {[
                {
                  icon: ArrowUpDownIcon,
                  title: "Seamless Transfers",
                  description:
                    "Send and receive money instantly between different digital platforms without using ZIMSWITCH.",
                },
                {
                  icon: ShieldCheckIcon,
                  title: "Enhanced Security",
                  description:
                    "Multi-factor authentication, encrypted transactions, and advanced fraud detection to keep your money safe.",
                },
                {
                  icon: SmartphoneIcon,
                  title: "All-In-One Solution",
                  description:
                    "Manage all your wallets (EcoCash, OneMoney, InnBucks, and O'mari) from a single application.",
                },
                {
                  icon: CreditCardIcon,
                  title: "Bill Payments",
                  description:
                    "Pay all your bills from any of your connected wallets with just a few taps.",
                },
                {
                  icon: BarChart3Icon,
                  title: "Financial Insights",
                  description:
                    "Track your spending, set budgets, and gain insights into your financial habits.",
                },
                {
                  icon: CheckIcon,
                  title: "Lower Fees",
                  description:
                    "Save money with lower transaction fees compared to traditional payment methods.",
                },
              ].map((feature, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center rounded-lg border bg-background p-6 text-center shadow-sm"
                >
                  <div className="rounded-full bg-primary/10 p-3">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="mt-4 text-xl font-semibold">{feature.title}</h3>
                  <p className="mt-2 text-muted-foreground">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section
          id="benefits"
          className="w-full px-4 py-16 md:py-24 lg:py-32"
        >
          <div className="container space-y-12">
            <div className="space-y-4 text-center">
              <h2 className="text-3xl font-bold sm:text-4xl">
                Why Choose ZB Digital Wallet?
              </h2>
              <p className="mx-auto max-w-[42rem] text-muted-foreground">
                Our wallet offers unique benefits for both individuals and businesses
                in Zimbabwe.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="rounded-lg border bg-muted/30 p-6">
                <h3 className="text-2xl font-semibold">For Individuals</h3>
                <ul className="mt-4 space-y-3">
                  {[
                    "Manage all your digital money accounts in one place",
                    "No need to maintain separate wallets or applications",
                    "Lower transaction fees compared to traditional methods",
                    "Seamless money transfers across different platforms",
                    "Enhanced security features to protect your funds",
                    "Easy bill payments from any of your connected wallets",
                  ].map((benefit, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckIcon className="mt-1 h-5 w-5 flex-shrink-0 text-primary" />
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="rounded-lg border bg-muted/30 p-6">
                <h3 className="text-2xl font-semibold">For Businesses</h3>
                <ul className="mt-4 space-y-3">
                  {[
                    "Accept payments from multiple digital money platforms",
                    "Single point of integration for all payment methods",
                    "Reduce dependency on expensive POS machines",
                    "Real-time transaction notifications and reporting",
                    "Improved cash flow management with instant access to funds",
                    "Detailed analytics to understand customer payment preferences",
                  ].map((benefit, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckIcon className="mt-1 h-5 w-5 flex-shrink-0 text-primary" />
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </section>

        <section
          id="wallets"
          className="w-full bg-muted/30 px-4 py-16 md:py-24 lg:py-32"
        >
          <div className="container space-y-12">
            <div className="space-y-4 text-center">
              <h2 className="text-3xl font-bold sm:text-4xl">
                Supported Digital Money Platforms
              </h2>
              <p className="mx-auto max-w-[42rem] text-muted-foreground">
                Integrate all your existing wallets into our universal platform.
              </p>
            </div>
            <div className="grid grid-cols-2 gap-6 sm:grid-cols-2 md:grid-cols-4">
              {[
                {
                  name: "EcoCash",
                  logo: "/images/wallets/ecocash.png",
                  description: "Zimbabwe's largest mobile money service",
                },
                {
                  name: "OneMoney",
                  logo: "/images/wallets/onemoney.png",
                  description: "NetOne's mobile money platform",
                },
                {
                  name: "InnBucks",
                  logo: "/images/wallets/innbucks.png",
                  description: "Innovative digital payment service",
                },
                {
                  name: "O'mari",
                  logo: "/images/wallets/omari.png",
                  description: "Fast-growing mobile money solution",
                },
              ].map((wallet, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center rounded-lg border bg-background p-6 text-center shadow-sm"
                >
                  <div className="h-16 w-16">
                    <Image
                      src={wallet.logo}
                      alt={`${wallet.name} Logo`}
                      width={64}
                      height={64}
                      className="h-full w-full object-contain"
                    />
                  </div>
                  <h3 className="mt-4 text-xl font-semibold">{wallet.name}</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    {wallet.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section
          id="cta"
          className="w-full bg-primary/10 px-4 py-16 md:py-24 lg:py-32"
        >
          <div className="container flex flex-col items-center gap-6 text-center">
            <h2 className="text-3xl font-bold sm:text-4xl">
              Ready to Simplify Your Digital Finances?
            </h2>
            <p className="max-w-[42rem] text-lg text-muted-foreground">
              Join thousands of Zimbabweans who are already enjoying the
              convenience of our Universal Digital Wallet.
            </p>
            <div className="flex flex-wrap items-center justify-center gap-4">
              <Link href="/register">
                <Button size="lg" className="gap-2">
                  Create Your Wallet
                  <ArrowRightIcon className="h-4 w-4" />
                </Button>
              </Link>
              <Link href="/login">
                <Button variant="outline" size="lg">
                  Log In
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t bg-muted/50 py-10">
        <div className="container grid gap-8 sm:grid-cols-2 md:grid-cols-4">
          <div className="space-y-4">
            <Link href="/" className="flex items-center gap-2">
              <Image
                src="/images/zb-logo.png"
                alt="ZB Digital Wallet"
                width={40}
                height={40}
                className="h-8 w-auto"
              />
              <span className="font-semibold">ZB Digital Wallet</span>
            </Link>
            <p className="text-sm text-muted-foreground">
              A product of ZB Financial Holdings Limited. Your universal digital
              wallet for Zimbabwe.
            </p>
          </div>
          <div>
            <h3 className="mb-4 text-sm font-semibold">Product</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link href="#features" className="hover:text-foreground">
                  Features
                </Link>
              </li>
              <li>
                <Link href="#benefits" className="hover:text-foreground">
                  Benefits
                </Link>
              </li>
              <li>
                <Link href="#wallets" className="hover:text-foreground">
                  Supported Wallets
                </Link>
              </li>
              <li>
                <Link href="#pricing" className="hover:text-foreground">
                  Pricing
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="mb-4 text-sm font-semibold">Resources</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link href="#" className="hover:text-foreground">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="#" className="hover:text-foreground">
                  Documentation
                </Link>
              </li>
              <li>
                <Link href="#" className="hover:text-foreground">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="#" className="hover:text-foreground">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="mb-4 text-sm font-semibold">Company</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link href="#" className="hover:text-foreground">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="#" className="hover:text-foreground">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="#" className="hover:text-foreground">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="#" className="hover:text-foreground">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="container mt-8 border-t pt-8">
          <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} ZB Financial Holdings. All rights
              reserved.
            </p>
            <div className="flex items-center gap-4">
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
                <span className="sr-only">Twitter</span>
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
