{"extends": "./tsconfig.json", "compilerOptions": {"noEmit": false, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "useUnknownInCatchVariables": false, "alwaysStrict": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "allowUnusedLabels": true, "allowUnreachableCode": true}}