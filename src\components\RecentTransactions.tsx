"use client";

import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowDownIcon, ArrowUpIcon, ClockIcon } from "lucide-react";
import { format } from "date-fns";

export interface Transaction {
  id: string;
  type: 'incoming' | 'outgoing' | 'pending';
  amount: number;
  currency: string;
  description: string;
  timestamp: Date;
  status: 'completed' | 'pending' | 'failed';
  sender?: string;
  recipient?: string;
  reference?: string;
}

interface RecentTransactionsProps {
  transactions: Transaction[];
  limit?: number;
}

export function RecentTransactions({ transactions, limit = 5 }: RecentTransactionsProps) {
  const displayTransactions = transactions.slice(0, limit);

  return (
    <Card className="p-4">
      <h3 className="font-semibold mb-4">Recent Transactions</h3>
      <ScrollArea className="h-[300px]">
        <div className="space-y-4">
          {displayTransactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center justify-between p-2 hover:bg-accent rounded-lg"
            >
              <div className="flex items-center gap-3">
                <div className={`
                  p-2 rounded-full
                  ${transaction.type === 'incoming' ? 'bg-green-100 text-green-600' : ''}
                  ${transaction.type === 'outgoing' ? 'bg-red-100 text-red-600' : ''}
                  ${transaction.type === 'pending' ? 'bg-yellow-100 text-yellow-600' : ''}
                `}>
                  {transaction.type === 'incoming' && <ArrowDownIcon className="h-4 w-4" />}
                  {transaction.type === 'outgoing' && <ArrowUpIcon className="h-4 w-4" />}
                  {transaction.type === 'pending' && <ClockIcon className="h-4 w-4" />}
                </div>
                <div>
                  <p className="font-medium">{transaction.description}</p>
                  <p className="text-sm text-muted-foreground">
                    {format(transaction.timestamp, 'MMM d, yyyy HH:mm')}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-medium ${
                  transaction.type === 'incoming' ? 'text-green-600' : 
                  transaction.type === 'outgoing' ? 'text-red-600' : 
                  'text-yellow-600'
                }`}>
                  {transaction.type === 'outgoing' ? '-' : '+'}
                  {transaction.amount.toLocaleString()} {transaction.currency}
                </p>
                <p className="text-sm text-muted-foreground capitalize">
                  {transaction.status}
                </p>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </Card>
  );
}
