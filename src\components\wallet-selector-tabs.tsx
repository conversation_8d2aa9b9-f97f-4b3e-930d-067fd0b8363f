import { motion } from 'framer-motion'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tabs<PERSON>rigger, TabsContent } from "@/components/ui/tabs"
import { useWalletStore, type Wallet, type WalletType } from '@/lib/stores/wallet-store'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import { Plus } from 'lucide-react'

interface WalletSelectorTabsProps {
  onWalletSelect?: (walletId: string) => void
  selectedWalletId?: string
  onAddWallet?: () => void
}

export function WalletSelectorTabs({ onWalletSelect, selectedWalletId, onAddWallet }: WalletSelectorTabsProps) {
  const { wallets } = useWalletStore()

  const mobileWallets = wallets.filter((wallet: Wallet) => wallet.type === 'mobile')
  const bankWallets = wallets.filter((wallet: Wallet) => wallet.type === 'bank')

  return (
    <Tabs defaultValue="all" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="all">All Wallets</TabsTrigger>
        <TabsTrigger value="connected">Connected</TabsTrigger>
      </TabsList>
      <TabsContent value="all" className="mt-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {wallets.map((wallet: Wallet) => (
            <Card
              key={wallet.id}
              className={cn(
                "p-4 cursor-pointer hover:bg-accent transition-colors",
                selectedWalletId === wallet.id && "ring-2 ring-primary"
              )}
              onClick={() => onWalletSelect?.(wallet.id)}
            >
              <div className="flex items-center gap-4">
                <div className="relative h-12 w-12">
                  <Image
                    src={wallet.logo}
                    alt={wallet.logoAlt}
                    fill
                    className="object-contain"
                  />
                </div>
                <div>
                  <h3 className="font-semibold">{wallet.name}</h3>
                  <p className="text-sm text-muted-foreground">{wallet.type}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </TabsContent>
      <TabsContent value="connected" className="mt-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {wallets
            .filter((wallet: Wallet) => wallet.isConnected)
            .map((wallet: Wallet) => (
              <Card
                key={wallet.id}
                className={cn(
                  "p-4 cursor-pointer hover:bg-accent transition-colors",
                  selectedWalletId === wallet.id && "ring-2 ring-primary"
                )}
                onClick={() => onWalletSelect?.(wallet.id)}
              >
                <div className="flex items-center gap-4">
                  <div className="relative h-12 w-12">
                    <Image
                      src={wallet.logo}
                      alt={wallet.logoAlt}
                      fill
                      className="object-contain"
                    />
                  </div>
                  <div>
                    <h3 className="font-semibold">{wallet.name}</h3>
                    <p className="text-sm text-muted-foreground">{wallet.type}</p>
                  </div>
                </div>
              </Card>
            ))}
        </div>
      </TabsContent>
    </Tabs>
  )
} 