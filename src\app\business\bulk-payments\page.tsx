"use client";

import { useState } from 'react'
import { DashboardShell } from '@/components/dashboard-shell'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Upload, 
  Download, 
  FileText, 
  Users, 
  DollarSign, 
  CheckCircle, 
  AlertCircle,
  Trash2,
  Plus
} from 'lucide-react'

export default function BulkPaymentsPage() {
  const [paymentMethod, setPaymentMethod] = useState('manual')
  const [payments, setPayments] = useState([
    { id: 1, recipient: '<PERSON>', phone: '+263771234567', amount: 500, description: 'Salary - October' },
    { id: 2, recipient: 'Jane Smith', phone: '+263772345678', amount: 750, description: 'Salary - October' },
    { id: 3, recipient: 'Mike Johnson', phone: '+263773456789', amount: 600, description: 'Salary - October' }
  ])
  const [newPayment, setNewPayment] = useState({
    recipient: '',
    phone: '',
    amount: '',
    description: ''
  })

  const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0)

  const addPayment = () => {
    if (newPayment.recipient && newPayment.phone && newPayment.amount) {
      setPayments([...payments, {
        id: Date.now(),
        recipient: newPayment.recipient,
        phone: newPayment.phone,
        amount: parseFloat(newPayment.amount),
        description: newPayment.description
      }])
      setNewPayment({ recipient: '', phone: '', amount: '', description: '' })
    }
  }

  const removePayment = (id: number) => {
    setPayments(payments.filter(p => p.id !== id))
  }

  const processBulkPayment = () => {
    // Mock processing
    alert(`Processing ${payments.length} payments totaling USD ${totalAmount.toLocaleString()}`)
  }

  return (
    <DashboardShell>
      <div className="container px-4 sm:px-6 space-y-6 sm:space-y-8 pb-20 md:pb-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Bulk Payments</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Process multiple payments efficiently
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Template
            </Button>
            <Button variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              History
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Recipients</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{payments.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">USD {totalAmount.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Ready</div>
            </CardContent>
          </Card>
        </div>

        {/* Payment Input Methods */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Setup</CardTitle>
            <CardDescription>Choose how to add payment recipients</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={paymentMethod} onValueChange={setPaymentMethod}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="manual">Manual Entry</TabsTrigger>
                <TabsTrigger value="upload">File Upload</TabsTrigger>
              </TabsList>

              <TabsContent value="manual" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="recipient">Recipient Name</Label>
                    <Input
                      id="recipient"
                      placeholder="John Doe"
                      value={newPayment.recipient}
                      onChange={(e) => setNewPayment({...newPayment, recipient: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      placeholder="+263 77 123 4567"
                      value={newPayment.phone}
                      onChange={(e) => setNewPayment({...newPayment, phone: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (USD)</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="500.00"
                      value={newPayment.amount}
                      onChange={(e) => setNewPayment({...newPayment, amount: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      placeholder="Salary - October"
                      value={newPayment.description}
                      onChange={(e) => setNewPayment({...newPayment, description: e.target.value})}
                    />
                  </div>
                </div>
                <Button onClick={addPayment} className="w-full sm:w-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Payment
                </Button>
              </TabsContent>

              <TabsContent value="upload" className="space-y-4">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Upload Payment File</h3>
                  <p className="text-muted-foreground mb-4">
                    Upload a CSV or Excel file with payment details
                  </p>
                  <Button variant="outline">
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                  </Button>
                </div>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    File should contain columns: Recipient Name, Phone Number, Amount, Description
                  </AlertDescription>
                </Alert>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Payment List */}
        {payments.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Payment Recipients ({payments.length})</CardTitle>
              <CardDescription>Review and modify payment details before processing</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {payments.map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div>
                          <p className="font-medium">{payment.recipient}</p>
                          <p className="text-sm text-muted-foreground">{payment.phone}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">USD {payment.amount.toLocaleString()}</p>
                          <p className="text-sm text-muted-foreground">{payment.description}</p>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removePayment(payment.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Process Payment */}
        {payments.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Process Bulk Payment</CardTitle>
              <CardDescription>Review and confirm payment details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Payment Source</Label>
                  <Select defaultValue="business-account">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="business-account">Business Account (USD 85,000)</SelectItem>
                      <SelectItem value="payroll-account">Payroll Account (USD 45,000)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Processing Date</Label>
                  <Select defaultValue="immediate">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="immediate">Process Immediately</SelectItem>
                      <SelectItem value="scheduled">Schedule for Later</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  You are about to process {payments.length} payments totaling USD {totalAmount.toLocaleString()}. 
                  This action cannot be undone.
                </AlertDescription>
              </Alert>

              <div className="flex space-x-2">
                <Button onClick={processBulkPayment} className="flex-1">
                  Process {payments.length} Payments
                </Button>
                <Button variant="outline">Save as Draft</Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardShell>
  )
}
