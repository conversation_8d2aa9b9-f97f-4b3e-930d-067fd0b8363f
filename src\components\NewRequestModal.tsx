'use client'

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { useWalletStore } from "@/lib/stores/wallet-store"
import { useToast } from "@/components/ui/use-toast"
import Image from "next/image"
import { Checkbox } from "@/components/ui/checkbox"

interface NewRequestModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function NewRequestModal({ open, onOpenChange }: NewRequestModalProps) {
  const [requestType, setRequestType] = useState<"personal" | "business">("personal")
  const [recipient, setRecipient] = useState("")
  const [amount, setAmount] = useState("")
  const [currency, setCurrency] = useState("USD")
  const [description, setDescription] = useState("")
  const [generateInvoice, setGenerateInvoice] = useState(false)
  const [businessDetails, setBusinessDetails] = useState({
    businessName: "",
    taxNumber: "",
    address: "",
    dueDate: "",
  })
  const [selectedWallet, setSelectedWallet] = useState("")
  const [selectedWalletType, setSelectedWalletType] = useState<'all' | 'mobile' | 'bank'>('all')
  const { wallets } = useWalletStore()
  const { toast } = useToast()

  // Filter connected wallets
  const connectedWallets = wallets.filter(wallet => wallet.state === 'connected')
  
  // Find ZBUniWallet
  const zbUniWallet = connectedWallets.find(wallet => wallet.name === 'ZBUniWallet')
  
  // Set default wallet to ZBUniWallet if available
  useEffect(() => {
    if (zbUniWallet) {
      setSelectedWallet(zbUniWallet.id)
    } else if (connectedWallets.length > 0) {
      setSelectedWallet(connectedWallets[0].id)
    }
  }, [zbUniWallet, connectedWallets])

  // Filter connected wallets by type
  const filteredWallets = connectedWallets.filter(wallet => {
    if (selectedWalletType === 'all') return true
    return wallet.type === selectedWalletType
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Clear form
      setRequestType("personal")
      setRecipient("")
      setAmount("")
      setCurrency("USD")
      setDescription("")
      setGenerateInvoice(false)
      setBusinessDetails({
        businessName: "",
        taxNumber: "",
        address: "",
        dueDate: "",
      })
      setSelectedWallet("")
      setSelectedWalletType("all")
      
      toast({
        title: "Success!",
        description: "Payment request has been created and sent.",
        variant: "default",
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create payment request. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[500px] max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>New Payment Request</DialogTitle>
          <DialogDescription>
            Create a new payment request to send to someone
          </DialogDescription>
        </DialogHeader>
        
        <div className="overflow-y-auto pr-2 space-y-4 max-h-[calc(80vh-140px)]">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label>Request Type</Label>
              <RadioGroup 
                value={requestType} 
                onValueChange={(value: "personal" | "business") => setRequestType(value)}
              >
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="personal" id="personal" />
                    <Label htmlFor="personal">Personal</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="business" id="business" />
                    <Label htmlFor="business">Business</Label>
                  </div>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Payer Phone Number</Label>
              <Input
                id="phone"
                placeholder="Enter payer's phone number"
                type="tel"
                value={recipient}
                onChange={(e) => setRecipient(e.target.value)}
              />
            </div>
              
            <div className="space-y-2">
              <Label htmlFor="email">Payer Email (Optional)</Label>
              <Input
                id="email"
                placeholder="Enter payer's email address"
                type="email"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="wallet">Destination Wallet</Label>
              <Select value={selectedWallet} onValueChange={setSelectedWallet}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a wallet" />
                </SelectTrigger>
                <SelectContent>
                  {connectedWallets.map((wallet) => (
                    <SelectItem key={wallet.id} value={wallet.id}>
                      <div className="flex items-center gap-2">
                        <Image
                          src={wallet.logo}
                          alt={wallet.name}
                          width={32}
                          height={32}
                          className="h-8 w-8"
                        />
                        <span>{wallet.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                placeholder="Enter amount"
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select value={currency} onValueChange={setCurrency}>
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="ZWL">ZWL</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                placeholder="Enter description"
                className="h-20"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            </div>

            {requestType === "business" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name</Label>
                  <Input
                    id="businessName"
                    placeholder="Enter business name"
                    value={businessDetails.businessName}
                    onChange={(e) => setBusinessDetails(prev => ({ ...prev, businessName: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taxNumber">Tax Number (Optional)</Label>
                  <Input
                    id="taxNumber"
                    placeholder="Enter tax number"
                    value={businessDetails.taxNumber}
                    onChange={(e) => setBusinessDetails(prev => ({ ...prev, taxNumber: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Business Address</Label>
                  <Textarea
                    id="address"
                    placeholder="Enter business address"
                    className="h-20"
                    value={businessDetails.address}
                    onChange={(e) => setBusinessDetails(prev => ({ ...prev, address: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="generateInvoice"
                      checked={generateInvoice}
                      onCheckedChange={(checked) => setGenerateInvoice(checked)}
                    />
                    <Label htmlFor="generateInvoice">Generate Invoice</Label>
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
              <Button type="submit">Create Request</Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
} 