'use client'

import { WalletList } from '@/components/WalletList'
import { RecentTransactions } from '@/components/RecentTransactions'
import { QuickActions } from '@/components/QuickActions'
import { transactions } from '@/lib/data'

export default function DashboardPage() {
  return (
    <div className="space-y-8">
      <WalletList />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <RecentTransactions transactions={transactions} />
        <QuickActions />
      </div>
    </div>
  )
} 