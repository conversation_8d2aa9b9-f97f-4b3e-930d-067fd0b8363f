# ZB UniWallet Platform

A comprehensive digital wallet solution that integrates multiple payment platforms in Zimbabwe.

## Overview

ZB UniWallet is a universal digital wallet platform that seamlessly connects various digital money services into a single, unified solution. It serves as a comprehensive financial bridge, allowing users to manage and transact across multiple digital money platforms including EcoCash, OneMoney, InnBucks, and O'mari.

## Features

- **Unified Wallet Management**
  - Single dashboard for all digital wallets
  - Real-time balance tracking
  - Multi-currency support (USD & ZWL)
  - Transaction history across all platforms

- **Card Management**
  - Virtual and physical card support
  - Multiple card types (Visa, Mastercard, ZB UniCard)
  - Card status monitoring
  - Transaction limits management

- **Money Transfers**
  - Cross-platform transfers
  - International remittances
  - QR code payments
  - Request money feature

- **Bill Payments**
  - Utility bill payments
  - Merchant payments
  - Airtime and data purchases
  - Schedule recurring payments

- **Business Tools**
  - Payment request generation
  - Invoice management
  - Business analytics
  - Multi-user access

## Technology Stack

- Next.js 14.1.0
- React 18
- TypeScript
- Tailwind CSS
- Shadcn UI Components
- Zustand for State Management

## Getting Started

### Prerequisites

- Node.js 18.0 or later
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/zb-digital-wallet.git
cd zb-digital-wallet
```

2. Install dependencies:
```bash
npm install
```

3. Create environment files:
```bash
cp .env.example .env.local
```

4. Start development server:
```bash
npm run dev
```

### Production Build

1. Create production build:
```bash
npm run build
```

2. The static files will be generated in the `out` directory.

### Deployment

1. Upload the contents of the `out` directory to your web server
2. Ensure the `.htaccess` file is included for proper routing
3. Configure your web server according to the deployment guides

## Project Structure

```
zb-digital-wallet/
├── src/
│   ├── app/                 # Next.js app directory
│   ├── components/          # Reusable components
│   ├── lib/                 # Utilities and helpers
│   └── styles/             # Global styles
├── public/                  # Static assets
├── out/                     # Production build output
└── docs/                    # Additional documentation
```

## Configuration

### Environment Variables

Create a `.env.local` file with the following variables:
```env
NEXT_PUBLIC_API_URL=your_api_url
NEXT_PUBLIC_ENVIRONMENT=development
```

### Web Server Configuration

The project includes necessary server configurations:
- `.htaccess` for Apache servers
- `web.config` for IIS servers
- Nginx configuration guide in `docs/nginx.md`

## Development Guidelines

### Code Style

- Follow TypeScript best practices
- Use functional components with hooks
- Implement proper error handling
- Write meaningful component documentation
- Follow the established project structure

### Testing

```bash
npm run test        # Run unit tests
npm run test:e2e    # Run end-to-end tests
npm run test:ci     # Run all tests in CI environment
```

## Security Features

- Multi-factor authentication
- Encrypted transactions
- Session management
- Rate limiting
- CORS configuration
- Security headers

## License

This project is proprietary software owned by ZB Financial Holdings Limited.

## Support

For technical support, please contact:
- Email: <EMAIL>
- Phone: +263-242-758280
- Website: https://www.zb.co.zw
