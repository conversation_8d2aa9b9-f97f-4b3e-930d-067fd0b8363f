"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function RequestMoneyRedirect() {
  const router = useRouter();

  useEffect(() => {
    router.replace("/sent-requests");
  }, [router]);

  return (
    <div className="flex h-screen w-full items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Redirecting...</h1>
        <p className="text-muted-foreground">
          This page has been moved to Sent Requests
        </p>
      </div>
    </div>
  );
}
