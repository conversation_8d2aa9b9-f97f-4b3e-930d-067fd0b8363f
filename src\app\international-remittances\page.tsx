"use client";

import { useState } from "react";
import { DashboardShell } from "@/components/dashboard-shell";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useWalletStore } from "@/lib/stores/wallet-store";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

const remittanceServices = [
  { id: "mukuru", name: "<PERSON><PERSON><PERSON>" },
  { id: "moneygram", name: "MoneyGram" },
  { id: "westernunion", name: "Western Union" },
  { id: "worldremit", name: "World Remit" },
  { id: "getbucks", name: "GetBucks" }
];

export default function InternationalRemittancesPage() {
  const { wallets } = useWalletStore();
  const [transactionType, setTransactionType] = useState<"receive" | "send">("receive");
  const [selectedService, setSelectedService] = useState("");
  const [amount, setAmount] = useState("");
  const [selectedWallet, setSelectedWallet] = useState("");
  const [destinationCountry, setDestinationCountry] = useState("");
  const [recipientId, setRecipientId] = useState("");
  const [recipientPhone, setRecipientPhone] = useState("");
  const { toast } = useToast();

  const countries = [
    { id: "ke", name: "Kenya" },
    { id: "tz", name: "Tanzania" },
    { id: "ug", name: "Uganda" },
    { id: "za", name: "South Africa" },
    { id: "zm", name: "Zambia" },
    { id: "mw", name: "Malawi" },
    { id: "mz", name: "Mozambique" },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedService || !amount || !selectedWallet || (transactionType === "send" && (!destinationCountry || !recipientId || !recipientPhone))) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success!",
        description: `Successfully processed ${transactionType === "receive" ? "receive" : "send"} remittance via ${remittanceServices.find(s => s.id === selectedService)?.name}`,
      });
      
      // Reset form
      setSelectedService("");
      setAmount("");
      setSelectedWallet("");
      setDestinationCountry("");
      setRecipientId("");
      setRecipientPhone("");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process remittance. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <DashboardShell>
      <Card>
        <CardHeader>
          <CardTitle>International Remittances</CardTitle>
          <CardDescription>Send or receive money from international transfer services</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label>Transaction Type</Label>
              <Tabs 
                defaultValue="receive" 
                value={transactionType} 
                onValueChange={(value) => setTransactionType(value as "receive" | "send")} 
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="receive">Receive Money</TabsTrigger>
                  <TabsTrigger value="send">Send Money</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {transactionType === "send" ? (
              <>
                <div className="space-y-2">
                  <Label htmlFor="wallet">Source Wallet</Label>
                  <Select value={selectedWallet} onValueChange={setSelectedWallet}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select source wallet" />
                    </SelectTrigger>
                    <SelectContent>
                      {wallets.map((wallet) => (
                        <SelectItem key={wallet.id} value={wallet.id}>
                          {wallet.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="service">Remittance Service</Label>
                  <Select value={selectedService} onValueChange={setSelectedService}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a service" />
                    </SelectTrigger>
                    <SelectContent>
                      {remittanceServices.map((service) => (
                        <SelectItem key={service.id} value={service.id}>
                          {service.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Destination Country</Label>
                  <Select value={destinationCountry} onValueChange={setDestinationCountry}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select destination country" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem key={country.id} value={country.id}>
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="recipientId">Recipient ID/Passport Number</Label>
                  <Input
                    id="recipientId"
                    placeholder="Enter recipient's ID or passport number"
                    value={recipientId}
                    onChange={(e) => setRecipientId(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="recipientPhone">Recipient Phone Number</Label>
                  <Input
                    id="recipientPhone"
                    placeholder="Enter recipient's phone number"
                    value={recipientPhone}
                    onChange={(e) => setRecipientPhone(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="amount">Amount to Send</Label>
                  <Input
                    id="amount"
                    type="number"
                    placeholder="Enter amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                  />
                </div>
              </>
            ) : (
              <>
                <div className="space-y-2">
                  <Label htmlFor="service">Remittance Service</Label>
                  <Select value={selectedService} onValueChange={setSelectedService}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a service" />
                    </SelectTrigger>
                    <SelectContent>
                      {remittanceServices.map((service) => (
                        <SelectItem key={service.id} value={service.id}>
                          {service.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="amount">Amount to Receive</Label>
                  <Input
                    id="amount"
                    type="number"
                    placeholder="Enter amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="wallet">Destination Wallet</Label>
                  <Select value={selectedWallet} onValueChange={setSelectedWallet}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select destination wallet" />
                    </SelectTrigger>
                    <SelectContent>
                      {wallets.map((wallet) => (
                        <SelectItem key={wallet.id} value={wallet.id}>
                          {wallet.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}

            <Button 
              type="submit" 
              className="w-full"
              disabled={!selectedService || !amount || !selectedWallet || (transactionType === "send" && (!destinationCountry || !recipientId || !recipientPhone))}
            >
              Process Remittance
            </Button>
          </form>
        </CardContent>
      </Card>
    </DashboardShell>
  );
} 