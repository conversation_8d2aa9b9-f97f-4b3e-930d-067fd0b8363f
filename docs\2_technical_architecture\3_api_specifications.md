# 3. API Specifications

## 🎯 **Module-Based API Overview**

The UniversalWallet API is organized into 15 independent module-based API groups, each supporting specific business capabilities. This modular approach enables independent development, deployment, and scaling of API services.

## 🏗️ **API Architecture**

### **Base Configuration**
```yaml
API_Base_URL: "https://api.universalwallet.co.zw/v1"
Authentication: "Bearer JWT tokens"
Content_Type: "application/json"
Rate_Limiting: "1000 requests/hour per user"
Versioning: "URL path versioning (/v1/, /v2/)"
```

### **Standard Response Format**
```json
{
  "success": true,
  "data": {},
  "error": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_********9"
}
```

---

## 👤 **User Management Module APIs (UW-USER)**

### **User Registration and Authentication**
```yaml
POST /user-management/register:
  description: "User registration with phone verification"
  module: "UW-USER"
  request_body:
    phoneNumber: "+************"
    pin: "1234"
    userType: "individual|business|merchant|agent"
  response: { userId, status: "pending_verification" }
  user_journey: "Individual/Business/Agent Registration Journey"

POST /user-management/verify-otp:
  description: "OTP verification for account activation"
  module: "UW-USER"
  request_body:
    phoneNumber: "+************"
    otp: "123456"
  response: { verified: true, accessToken, refreshToken }
  user_journey: "Registration Verification Journey"

POST /user-management/login:
  description: "User authentication with PIN or biometric"
  module: "UW-USER"
  request_body:
    phoneNumber: "+************"
    pin: "1234"
    biometricData: "optional_biometric_hash"
  response: { accessToken, refreshToken, user }
  user_journey: "Authentication Journey (Cross-Module)"

POST /user-management/refresh-token:
  description: "Refresh access token using refresh token"
  module: "UW-USER"
  request_body:
    refreshToken: "refresh_token_string"
  response: { accessToken, refreshToken }
  user_journey: "Session Management Journey"
```

### **User Profile Management**
```yaml
GET /users/{userId}/profile:
  description: "Retrieve user profile information"
  response: { profile, kycStatus, preferences }
  functional_mapping: [profile_viewing, kyc_status_checking]

PUT /users/{userId}/profile:
  description: "Update user profile information"
  request_body:
    firstName: "John"
    lastName: "Doe"
    dateOfBirth: "1990-01-01"
    address: { street, city, country }
  functional_mapping: [profile_updates, personal_information_management]

POST /users/{userId}/kyc/documents:
  description: "Upload KYC documents for verification"
  request_body:
    documentType: "national_id|passport|proof_of_address"
    documentImage: "base64_encoded_image"
  functional_mapping: [document_upload, kyc_submission]

PUT /users/{userId}/security:
  description: "Update security settings"
  request_body:
    newPin: "5678"
    biometricEnabled: true
    twoFactorEnabled: true
  functional_mapping: [pin_management, biometric_setup, 2fa_configuration]
```

---

## 💰 **Account and Balance Management APIs**

### **Account Linking**
```yaml
GET /users/{userId}/accounts:
  description: "List all linked accounts for user"
  response: { accounts: [{ accountId, provider, accountType, balance, status }] }
  functional_mapping: [linked_accounts_viewing, balance_aggregation]

POST /users/{userId}/accounts/link:
  description: "Link external account to UniversalWallet"
  request_body:
    provider: "ecocash|onemoney|innbucks|bank"
    accountNumber: "+************"
    credentials: { pin: "1234" }
  functional_mapping: [account_linking, provider_integration]

DELETE /users/{userId}/accounts/{accountId}:
  description: "Unlink account from UniversalWallet"
  functional_mapping: [account_unlinking, account_management]

POST /users/{userId}/accounts/{accountId}/refresh:
  description: "Refresh account balance and transaction data"
  functional_mapping: [balance_refresh, data_synchronization]
```

### **Balance Operations**
```yaml
GET /users/{userId}/balances:
  description: "Get aggregated balance across all accounts"
  response: { totalBalance, accountBalances, lastUpdated }
  functional_mapping: [balance_aggregation, unified_balance_viewing]

GET /users/{userId}/balances/history:
  description: "Get balance history over time"
  query_params:
    period: "7d|30d|90d|1y"
    accountId: "optional_specific_account"
  functional_mapping: [balance_history, financial_analytics]
```

---

## 💸 **Transaction and Payment APIs**

### **Money Transfers**
```yaml
POST /transactions/transfer:
  description: "Initiate money transfer between accounts"
  request_body:
    recipientPhone: "+************"
    amount: 100.00
    sourceAccountId: "account_123"
    description: "Payment for services"
    transferType: "p2p|business|agent"
  response: { transactionId, status: "pending", estimatedCompletion }
  functional_mapping: [p2p_transfers, interoperable_transfers]

POST /transactions/bulk-transfer:
  description: "Process bulk payments for businesses"
  request_body:
    payments: [{ recipientPhone, amount, description }]
    sourceAccountId: "business_account_123"
    approvalRequired: true
  functional_mapping: [bulk_payments, business_operations]

GET /transactions/{transactionId}:
  description: "Get transaction status and details"
  response: { transaction, status, timeline, receipt }
  functional_mapping: [transaction_tracking, status_monitoring]

GET /users/{userId}/transactions:
  description: "Get user transaction history"
  query_params:
    limit: 50
    offset: 0
    type: "all|transfer|payment|deposit|withdrawal"
    dateFrom: "2024-01-01"
    dateTo: "2024-01-31"
  functional_mapping: [transaction_history, financial_records]
```

### **Bill Payments**
```yaml
GET /bills/providers:
  description: "List available bill payment providers"
  query_params:
    category: "utilities|mobile|subscriptions"
  response: { providers: [{ id, name, category, paymentMethods }] }
  functional_mapping: [bill_provider_listing, payment_options]

POST /bills/pay:
  description: "Process bill payment"
  request_body:
    providerId: "zesa_electricity"
    accountNumber: "********"
    amount: 50.00
    sourceAccountId: "account_123"
  functional_mapping: [bill_payments, utility_payments]

POST /airtime/purchase:
  description: "Purchase airtime or data bundles"
  request_body:
    network: "econet|netone|telecel"
    phoneNumber: "+************"
    amount: 10.00
    productType: "airtime|data"
  functional_mapping: [airtime_purchase, mobile_services]
```

---

## 👥 **Group Savings APIs**

### **Group Management**
```yaml
POST /groups:
  description: "Create new savings group"
  request_body:
    name: "Family Savings Group"
    description: "Saving for vacation"
    groupType: "rotating|goal_based|investment|emergency"
    targetAmount: 5000.00
    contributionFrequency: "weekly|monthly"
    memberLimit: 10
  functional_mapping: [group_creation, savings_goal_setting]

GET /groups/{groupId}:
  description: "Get group details and progress"
  response: { group, members, balance, progress, nextContribution }
  functional_mapping: [group_viewing, progress_tracking]

POST /groups/{groupId}/join:
  description: "Request to join savings group"
  request_body:
    groupCode: "ABC123"
    message: "Please add me to the group"
  functional_mapping: [group_joining, member_requests]

POST /groups/{groupId}/contributions:
  description: "Make contribution to group"
  request_body:
    amount: 100.00
    sourceAccountId: "account_123"
    note: "Monthly contribution"
  functional_mapping: [group_contributions, savings_deposits]
```

---

## 🏢 **Business APIs**

### **Business Operations**
```yaml
POST /business/{businessId}/invoices:
  description: "Create business invoice"
  request_body:
    customerId: "customer_123"
    lineItems: [{ description, quantity, unitPrice, tax }]
    dueDate: "2024-02-15"
    paymentTerms: "Net 30"
  functional_mapping: [invoice_creation, business_billing]

POST /business/{businessId}/team/invite:
  description: "Invite team member to business account"
  request_body:
    email: "<EMAIL>"
    role: "admin|manager|operator|viewer"
    permissions: ["payments", "reporting", "user_management"]
  functional_mapping: [team_management, role_assignment]

GET /business/{businessId}/reports/financial:
  description: "Generate financial reports"
  query_params:
    reportType: "profit_loss|cash_flow|balance_sheet"
    period: "monthly|quarterly|yearly"
    year: 2024
  functional_mapping: [financial_reporting, business_analytics]
```

---

## 🤝 **Agent APIs**

### **Agent Operations**
```yaml
POST /agents/{agentId}/cash-in:
  description: "Process cash-in transaction for customer"
  request_body:
    customerPhone: "+************"
    amount: 200.00
    customerPresent: true
  functional_mapping: [cash_in_services, agent_transactions]

POST /agents/{agentId}/cash-out:
  description: "Process cash-out transaction for customer"
  request_body:
    customerPhone: "+************"
    amount: 150.00
    customerPin: "1234"
  functional_mapping: [cash_out_services, withdrawal_processing]

GET /agents/{agentId}/float:
  description: "Get agent float balance and limits"
  response: { currentFloat, minimumFloat, maximumFloat, lastTopUp }
  functional_mapping: [float_management, liquidity_monitoring]
```

---

## 👨‍💼 **Admin APIs**

### **System Administration**
```yaml
GET /admin/users:
  description: "List and manage user accounts"
  query_params:
    status: "active|suspended|pending"
    userType: "personal|business|agent"
    kycStatus: "pending|verified|rejected"
  functional_mapping: [user_management, account_administration]

PUT /admin/users/{userId}/status:
  description: "Update user account status"
  request_body:
    status: "active|suspended|closed"
    reason: "Compliance violation"
  functional_mapping: [account_status_management, compliance_actions]

GET /admin/reports/compliance:
  description: "Generate compliance and regulatory reports"
  query_params:
    reportType: "aml|kyc|transaction_monitoring"
    period: "daily|weekly|monthly"
  functional_mapping: [regulatory_reporting, compliance_monitoring]
```

**This consolidated API specification provides complete endpoint coverage for all platform functionality, with clear functional mapping and standardized request/response formats across all user types.** 🎯
