"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { useUserStore } from "@/lib/stores/userStore";
import { Eye, EyeOff, AlertCircle, CheckCircle, User, Building2, Users, Smartphone } from "lucide-react";

export default function RegisterPage() {
  const router = useRouter();
  const { setUser } = useUserStore();
  const [step, setStep] = useState(1);
  const [userType, setUserType] = useState<'personal' | 'business' | 'agent'>('personal');
  const [formData, setFormData] = useState({
    phoneNumber: '',
    email: '',
    firstName: '',
    lastName: '',
    businessName: '',
    businessType: '',
    pin: '',
    confirmPin: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false,
    agreePrivacy: false
  });
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const progress = (step / 4) * 100;

  const handleNext = () => {
    if (validateStep()) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const validateStep = () => {
    setError('');
    
    switch (step) {
      case 1:
        if (!userType) {
          setError('Please select an account type');
          return false;
        }
        break;
      case 2:
        if (!formData.phoneNumber || !formData.email) {
          setError('Please fill in all required fields');
          return false;
        }
        if (userType === 'personal' && (!formData.firstName || !formData.lastName)) {
          setError('Please enter your full name');
          return false;
        }
        if (userType === 'business' && (!formData.businessName || !formData.businessType)) {
          setError('Please enter business details');
          return false;
        }
        break;
      case 3:
        if (!formData.pin || !formData.confirmPin) {
          setError('Please set your PIN');
          return false;
        }
        if (formData.pin !== formData.confirmPin) {
          setError('PINs do not match');
          return false;
        }
        if (formData.pin.length !== 4) {
          setError('PIN must be 4 digits');
          return false;
        }
        break;
      case 4:
        if (!otp || otp.length !== 6) {
          setError('Please enter the 6-digit verification code');
          return false;
        }
        break;
    }
    return true;
  };

  const handleRegister = async () => {
    if (!formData.agreeTerms || !formData.agreePrivacy) {
      setError('Please accept the terms and privacy policy');
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock successful registration
      const mockUser = {
        id: '1',
        name: userType === 'business' ? formData.businessName : `${formData.firstName} ${formData.lastName}`,
        email: formData.email,
        phone: formData.phoneNumber,
        userType,
        avatar: '/images/avatar.png',
        preferences: {
          theme: 'light' as const,
          notifications: true,
          language: 'en'
        }
      };

      setUser(mockUser);
      
      // Redirect based on user type
      if (userType === 'business') {
        router.push('/business/onboarding');
      } else if (userType === 'agent') {
        router.push('/agent/onboarding');
      } else {
        router.push('/onboarding');
      }
    } catch (err: any) {
      setError('Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const sendOTP = async () => {
    setIsLoading(true);
    try {
      // Simulate OTP sending
      await new Promise(resolve => setTimeout(resolve, 1000));
      setStep(4);
    } catch (err) {
      setError('Failed to send verification code');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <header className="container flex h-16 items-center justify-between py-4">
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/images/zb-logo.png"
            alt="ZB Digital Wallet"
            width={40}
            height={40}
            className="h-8 w-auto"
          />
          <span className="font-semibold">ZBUniWallet</span>
        </Link>
        <Link href="/login">
          <Button variant="ghost">Sign In</Button>
        </Link>
      </header>

      <main className="container flex flex-1 items-center justify-center py-10">
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">Create Account</CardTitle>
            <CardDescription>
              Join ZBUniWallet and manage all your finances in one place
            </CardDescription>
            <Progress value={progress} className="w-full" />
            <p className="text-xs text-muted-foreground">Step {step} of 4</p>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Step 1: Account Type Selection */}
            {step === 1 && (
              <div className="space-y-4">
                <Label>Choose Your Account Type</Label>
                <div className="grid gap-3">
                  <Card 
                    className={`cursor-pointer border-2 transition-colors ${userType === 'personal' ? 'border-primary' : 'border-muted'}`}
                    onClick={() => setUserType('personal')}
                  >
                    <CardContent className="flex items-center space-x-3 p-4">
                      <User className="h-8 w-8 text-primary" />
                      <div>
                        <h3 className="font-semibold">Personal Account</h3>
                        <p className="text-sm text-muted-foreground">For individual users</p>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card 
                    className={`cursor-pointer border-2 transition-colors ${userType === 'business' ? 'border-primary' : 'border-muted'}`}
                    onClick={() => setUserType('business')}
                  >
                    <CardContent className="flex items-center space-x-3 p-4">
                      <Building2 className="h-8 w-8 text-primary" />
                      <div>
                        <h3 className="font-semibold">Business Account</h3>
                        <p className="text-sm text-muted-foreground">For businesses and organizations</p>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card 
                    className={`cursor-pointer border-2 transition-colors ${userType === 'agent' ? 'border-primary' : 'border-muted'}`}
                    onClick={() => setUserType('agent')}
                  >
                    <CardContent className="flex items-center space-x-3 p-4">
                      <Users className="h-8 w-8 text-primary" />
                      <div>
                        <h3 className="font-semibold">Agent Account</h3>
                        <p className="text-sm text-muted-foreground">For financial service agents</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {/* Step 2: Personal Information */}
            {step === 2 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    placeholder="+263 77 123 4567"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => setFormData({...formData, phoneNumber: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    placeholder="<EMAIL>"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                  />
                </div>

                {userType === 'personal' && (
                  <>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">First Name *</Label>
                        <Input
                          id="firstName"
                          placeholder="John"
                          value={formData.firstName}
                          onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Last Name *</Label>
                        <Input
                          id="lastName"
                          placeholder="Doe"
                          value={formData.lastName}
                          onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                        />
                      </div>
                    </div>
                  </>
                )}

                {userType === 'business' && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="businessName">Business Name *</Label>
                      <Input
                        id="businessName"
                        placeholder="Acme Corporation"
                        value={formData.businessName}
                        onChange={(e) => setFormData({...formData, businessName: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Business Type *</Label>
                      <Select value={formData.businessType} onValueChange={(value) => setFormData({...formData, businessType: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select business type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sole-proprietorship">Sole Proprietorship</SelectItem>
                          <SelectItem value="partnership">Partnership</SelectItem>
                          <SelectItem value="private-company">Private Company</SelectItem>
                          <SelectItem value="public-company">Public Company</SelectItem>
                          <SelectItem value="ngo">NGO/Non-Profit</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>
            )}

            {/* Step 3: Security Setup */}
            {step === 3 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="pin">Create 4-Digit PIN *</Label>
                  <Input
                    id="pin"
                    type="password"
                    placeholder="••••"
                    maxLength={4}
                    value={formData.pin}
                    onChange={(e) => setFormData({...formData, pin: e.target.value})}
                    className="text-center text-lg tracking-widest"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPin">Confirm PIN *</Label>
                  <Input
                    id="confirmPin"
                    type="password"
                    placeholder="••••"
                    maxLength={4}
                    value={formData.confirmPin}
                    onChange={(e) => setFormData({...formData, confirmPin: e.target.value})}
                    className="text-center text-lg tracking-widest"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password (Optional)</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Create a strong password"
                      value={formData.password}
                      onChange={(e) => setFormData({...formData, password: e.target.value})}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={formData.agreeTerms}
                    onCheckedChange={(checked) => setFormData({...formData, agreeTerms: checked as boolean})}
                  />
                  <Label htmlFor="terms" className="text-sm">
                    I agree to the <Link href="/terms" className="text-primary underline">Terms of Service</Link>
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="privacy"
                    checked={formData.agreePrivacy}
                    onCheckedChange={(checked) => setFormData({...formData, agreePrivacy: checked as boolean})}
                  />
                  <Label htmlFor="privacy" className="text-sm">
                    I agree to the <Link href="/privacy" className="text-primary underline">Privacy Policy</Link>
                  </Label>
                </div>
              </div>
            )}

            {/* Step 4: Phone Verification */}
            {step === 4 && (
              <div className="space-y-4 text-center">
                <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                  <Smartphone className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold">Verify Your Phone Number</h3>
                  <p className="text-sm text-muted-foreground">
                    We've sent a 6-digit code to {formData.phoneNumber}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="otp">Verification Code</Label>
                  <Input
                    id="otp"
                    type="text"
                    placeholder="123456"
                    maxLength={6}
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    className="text-center text-lg tracking-widest"
                  />
                </div>
                <Button variant="outline" size="sm">
                  Resend Code
                </Button>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </CardContent>

          <CardFooter className="flex justify-between">
            {step > 1 && (
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
            )}
            <div className="flex-1" />
            {step < 4 ? (
              <Button onClick={step === 3 ? sendOTP : handleNext} disabled={isLoading}>
                {step === 3 ? (isLoading ? 'Sending...' : 'Send Code') : 'Next'}
              </Button>
            ) : (
              <Button onClick={handleRegister} disabled={isLoading}>
                {isLoading ? 'Creating Account...' : 'Complete Registration'}
              </Button>
            )}
          </CardFooter>
        </Card>
      </main>

      <footer className="border-t bg-muted/50 py-6 text-center text-sm text-muted-foreground">
        <div className="container">
          &copy; {new Date().getFullYear()} ZB Financial Holdings. All rights reserved.
        </div>
      </footer>
    </div>
  );
}
